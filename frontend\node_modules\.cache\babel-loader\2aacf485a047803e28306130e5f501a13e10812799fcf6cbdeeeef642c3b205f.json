{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\productsCarousel.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Carousel, Image } from 'react-bootstrap';\nimport { formatVND } from '../utils/currency';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProductsCarousel(_ref) {\n  let {\n    products\n  } = _ref;\n  let topRatedProducts = [...products];\n  topRatedProducts.sort((a, b) => Number(b.rating) - Number(a.rating));\n  topRatedProducts = topRatedProducts.slice(0, 4);\n  return /*#__PURE__*/_jsxDEV(Carousel, {\n    pause: \"hover\",\n    className: \"bg-dark\",\n    children: topRatedProducts.map(product => /*#__PURE__*/_jsxDEV(Carousel.Item, {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: `/products/${product.id}`,\n        children: [/*#__PURE__*/_jsxDEV(Image, {\n          src: product.image,\n          alt: product.name,\n          style: {\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Carousel.Caption, {\n          className: \"carousel-caption\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [product.name, \" (\", formatVND(product.price), \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this)\n    }, product.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 49\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 9\n  }, this);\n}\n_c = ProductsCarousel;\nexport default ProductsCarousel;\nvar _c;\n$RefreshReg$(_c, \"ProductsCarousel\");", "map": {"version": 3, "names": ["React", "Link", "Carousel", "Image", "formatVND", "jsxDEV", "_jsxDEV", "ProductsCarousel", "_ref", "products", "topRatedProducts", "sort", "a", "b", "Number", "rating", "slice", "pause", "className", "children", "map", "product", "<PERSON><PERSON>", "to", "id", "src", "image", "alt", "name", "style", "objectFit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Caption", "price", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/productsCarousel.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Carousel, Image } from 'react-bootstrap';\nimport { formatVND } from '../utils/currency';\n\nfunction ProductsCarousel({products}) {\n    let topRatedProducts = [...products];\n    topRatedProducts.sort((a, b) => Number(b.rating) - Number(a.rating));\n    topRatedProducts = topRatedProducts.slice(0, 4);\n\n    return (\n        <Carousel pause=\"hover\" className='bg-dark'>\n            {topRatedProducts.map((product) => (<Carousel.Item key={product.id}>\n                <Link to={`/products/${product.id}`}>\n                    <Image src={product.image} alt={product.name} style={{objectFit:'cover'}}/>\n                    <Carousel.Caption className='carousel-caption'>\n                        <h4>{product.name} ({formatVND(product.price)})</h4></Carousel.Caption>\n                </Link>\n            </Carousel.Item>))}\n        </Carousel>\n    );\n}\n\nexport default ProductsCarousel;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,EAAEC,KAAK,QAAQ,iBAAiB;AACjD,SAASC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,gBAAgBA,CAAAC,IAAA,EAAa;EAAA,IAAZ;IAACC;EAAQ,CAAC,GAAAD,IAAA;EAChC,IAAIE,gBAAgB,GAAG,CAAC,GAAGD,QAAQ,CAAC;EACpCC,gBAAgB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKC,MAAM,CAACD,CAAC,CAACE,MAAM,CAAC,GAAGD,MAAM,CAACF,CAAC,CAACG,MAAM,CAAC,CAAC;EACpEL,gBAAgB,GAAGA,gBAAgB,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAE/C,oBACIV,OAAA,CAACJ,QAAQ;IAACe,KAAK,EAAC,OAAO;IAACC,SAAS,EAAC,SAAS;IAAAC,QAAA,EACtCT,gBAAgB,CAACU,GAAG,CAAEC,OAAO,iBAAMf,OAAA,CAACJ,QAAQ,CAACoB,IAAI;MAAAH,QAAA,eAC9Cb,OAAA,CAACL,IAAI;QAACsB,EAAE,EAAG,aAAYF,OAAO,CAACG,EAAG,EAAE;QAAAL,QAAA,gBAChCb,OAAA,CAACH,KAAK;UAACsB,GAAG,EAAEJ,OAAO,CAACK,KAAM;UAACC,GAAG,EAAEN,OAAO,CAACO,IAAK;UAACC,KAAK,EAAE;YAACC,SAAS,EAAC;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAE,eAC3E5B,OAAA,CAACJ,QAAQ,CAACiC,OAAO;UAACjB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC1Cb,OAAA;YAAAa,QAAA,GAAKE,OAAO,CAACO,IAAI,EAAC,IAAE,EAACxB,SAAS,CAACiB,OAAO,CAACe,KAAK,CAAC,EAAC,GAAC;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAK;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAmB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACxE,GAL6Cb,OAAO,CAACG,EAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAMjD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACX;AAEnB;AAACG,EAAA,GAhBQ9B,gBAAgB;AAkBzB,eAAeA,gBAAgB;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}