{"ast": null, "code": "/**\n * Utility functions for currency formatting\n */\n\n/**\n * Format number to Vietnamese Dong currency\n * @param {number} amount - The amount to format\n * @returns {string} - Formatted currency string\n */\nexport const formatVND = amount => {\n  if (typeof amount !== 'number') {\n    amount = parseFloat(amount) || 0;\n  }\n\n  // Remove decimal places and format with Vietnamese locale\n  const integerAmount = Math.round(amount);\n  return `${integerAmount.toLocaleString('vi-VN')} VND`;\n};\n\n/**\n * Format number to Vietnamese Dong currency without VND suffix\n * @param {number} amount - The amount to format\n * @returns {string} - Formatted number string\n */\nexport const formatNumber = amount => {\n  if (typeof amount !== 'number') {\n    amount = parseFloat(amount) || 0;\n  }\n\n  // Remove decimal places and format with Vietnamese locale\n  const integerAmount = Math.round(amount);\n  return integerAmount.toLocaleString('vi-VN');\n};\n\n/**\n * Currency constants for Vietnam\n */\nexport const CURRENCY = {\n  SYMBOL: 'VND',\n  LOCALE: 'vi-VN',\n  // Shipping thresholds in VND\n  FREE_SHIPPING_THRESHOLD: 2000000,\n  // 2 million VND\n  REDUCED_SHIPPING_THRESHOLD: 1000000,\n  // 1 million VND\n  // Shipping costs in VND\n  STANDARD_SHIPPING: 250000,\n  // 250k VND\n  REDUCED_SHIPPING: 100000,\n  // 100k VND\n  FREE_SHIPPING: 0\n};", "map": {"version": 3, "names": ["formatVND", "amount", "parseFloat", "integerAmount", "Math", "round", "toLocaleString", "formatNumber", "CURRENCY", "SYMBOL", "LOCALE", "FREE_SHIPPING_THRESHOLD", "REDUCED_SHIPPING_THRESHOLD", "STANDARD_SHIPPING", "REDUCED_SHIPPING", "FREE_SHIPPING"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/utils/currency.js"], "sourcesContent": ["/**\n * Utility functions for currency formatting\n */\n\n/**\n * Format number to Vietnamese Dong currency\n * @param {number} amount - The amount to format\n * @returns {string} - Formatted currency string\n */\nexport const formatVND = (amount) => {\n  if (typeof amount !== 'number') {\n    amount = parseFloat(amount) || 0;\n  }\n\n  // Remove decimal places and format with Vietnamese locale\n  const integerAmount = Math.round(amount);\n  return `${integerAmount.toLocaleString('vi-VN')} VND`;\n};\n\n/**\n * Format number to Vietnamese Dong currency without VND suffix\n * @param {number} amount - The amount to format\n * @returns {string} - Formatted number string\n */\nexport const formatNumber = (amount) => {\n  if (typeof amount !== 'number') {\n    amount = parseFloat(amount) || 0;\n  }\n\n  // Remove decimal places and format with Vietnamese locale\n  const integerAmount = Math.round(amount);\n  return integerAmount.toLocaleString('vi-VN');\n};\n\n/**\n * Currency constants for Vietnam\n */\nexport const CURRENCY = {\n  SYMBOL: 'VND',\n  LOCALE: 'vi-VN',\n  // Shipping thresholds in VND\n  FREE_SHIPPING_THRESHOLD: 2000000, // 2 million VND\n  REDUCED_SHIPPING_THRESHOLD: 1000000, // 1 million VND\n  // Shipping costs in VND\n  STANDARD_SHIPPING: 250000, // 250k VND\n  REDUCED_SHIPPING: 100000,  // 100k VND\n  FREE_SHIPPING: 0\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,GAAIC,MAAM,IAAK;EACnC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGC,UAAU,CAACD,MAAM,CAAC,IAAI,CAAC;EAClC;;EAEA;EACA,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;EACxC,OAAQ,GAAEE,aAAa,CAACG,cAAc,CAAC,OAAO,CAAE,MAAK;AACvD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAIN,MAAM,IAAK;EACtC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGC,UAAU,CAACD,MAAM,CAAC,IAAI,CAAC;EAClC;;EAEA;EACA,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;EACxC,OAAOE,aAAa,CAACG,cAAc,CAAC,OAAO,CAAC;AAC9C,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,QAAQ,GAAG;EACtBC,MAAM,EAAE,KAAK;EACbC,MAAM,EAAE,OAAO;EACf;EACAC,uBAAuB,EAAE,OAAO;EAAE;EAClCC,0BAA0B,EAAE,OAAO;EAAE;EACrC;EACAC,iBAAiB,EAAE,MAAM;EAAE;EAC3BC,gBAAgB,EAAE,MAAM;EAAG;EAC3BC,aAAa,EAAE;AACjB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}