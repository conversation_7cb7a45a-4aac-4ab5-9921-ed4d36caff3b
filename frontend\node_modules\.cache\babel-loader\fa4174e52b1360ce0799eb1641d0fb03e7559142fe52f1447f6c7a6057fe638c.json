{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\context\\\\cartContext.js\",\n  _s = $RefreshSig$();\nimport { createContext, useState, useContext } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport httpService from \"../services/httpService\";\nimport UserContext from './userContext';\nimport { CURRENCY } from \"../utils/currency\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartContext = /*#__PURE__*/createContext();\nexport default CartContext;\nexport const CartProvider = _ref => {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [error, setError] = useState(\"\");\n  let [productsInCart, setProductsInCart] = useState(localStorage.getItem(\"cartItems\") ? JSON.parse(localStorage.getItem(\"cartItems\")) : []);\n  const [shippingAddress, setShippingAddress] = useState(localStorage.getItem(\"shippingAddress\") ? JSON.parse(localStorage.getItem(\"shippingAddress\")) : {});\n  const [paymentMethod, setPaymentMethod] = useState(localStorage.getItem(\"paymentMethod\") ? localStorage.getItem(\"paymentMethod\") : \"Stripe\");\n  const navigate = useNavigate();\n  const {\n    logout\n  } = useContext(UserContext);\n  const addItemToCart = async (id, qty) => {\n    const item = productsInCart.find(prod => prod.id === Number(id));\n    if (item) {\n      updateItemQty(id, qty);\n      return;\n    }\n    try {\n      const {\n        data\n      } = await httpService.get(`/api/products/${id}/`);\n      const product = {\n        id: data.id,\n        name: data.name,\n        qty: qty,\n        image: data.image,\n        price: data.price,\n        countInStock: data.countInStock\n      };\n      localStorage.setItem(\"cartItems\", JSON.stringify([...productsInCart, product]));\n      setProductsInCart([...productsInCart, product]);\n    } catch (ex) {\n      setError(ex.message);\n    }\n  };\n  const updateItemQty = (id, qty) => {\n    const item = productsInCart.find(prod => prod.id === Number(id));\n    if (item.qty == Number(qty)) return;\n    const product = {\n      ...item\n    };\n    product.qty = Number(qty);\n    const updatedProductsInCart = productsInCart.map(prod => prod.id == product.id ? product : prod);\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedProductsInCart));\n    setProductsInCart(updatedProductsInCart);\n  };\n  const removeFromCart = id => {\n    const updatedProductsInCart = productsInCart.filter(prod => prod.id !== Number(id));\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedProductsInCart));\n    setProductsInCart(updatedProductsInCart);\n  };\n  const updateShippingAddress = (address, city, postalCode, country) => {\n    const newShippingAddress = {\n      address,\n      city,\n      postalCode,\n      country\n    };\n    setShippingAddress(newShippingAddress);\n    localStorage.setItem(\"shippingAddress\", JSON.stringify(newShippingAddress));\n  };\n  const updatePaymentMethod = method => {\n    setPaymentMethod(method);\n    localStorage.setItem(\"paymentMethod\", method);\n  };\n  const totalItemsPrice = Number(productsInCart.reduce((acc, prod) => acc + prod.qty * prod.price, 0).toFixed(2));\n  const shippingPrice = totalItemsPrice > CURRENCY.REDUCED_SHIPPING_THRESHOLD ? totalItemsPrice >= CURRENCY.FREE_SHIPPING_THRESHOLD ? CURRENCY.FREE_SHIPPING : CURRENCY.REDUCED_SHIPPING : CURRENCY.STANDARD_SHIPPING;\n  const taxPrice = Number((0.05 * totalItemsPrice).toFixed(2));\n  const totalPrice = totalItemsPrice + shippingPrice + taxPrice;\n  const placeOrder = async () => {\n    try {\n      const {\n        data\n      } = await httpService.post(\"/api/placeorder/\", {\n        orderItems: productsInCart,\n        shippingAddress,\n        paymentMethod,\n        itemsPrice: totalItemsPrice,\n        taxPrice,\n        shippingPrice,\n        totalPrice\n      });\n      console.log(data);\n      setProductsInCart([]);\n      localStorage.removeItem(\"cartItems\");\n      navigate(`/orders/${data.id}`);\n    } catch (ex) {\n      if (ex.response && ex.response.status == 403) logout();\n      console.log(ex.response);\n    }\n  };\n  const contextData = {\n    error,\n    productsInCart,\n    addItemToCart,\n    updateItemQty,\n    removeFromCart,\n    shippingAddress,\n    updateShippingAddress,\n    paymentMethod,\n    totalItemsPrice,\n    shippingPrice,\n    taxPrice,\n    totalPrice,\n    updatePaymentMethod,\n    placeOrder\n  };\n  return /*#__PURE__*/_jsxDEV(CartContext.Provider, {\n    value: contextData,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(CartProvider, \"5/cdx+w4Pcc0AmeiKSq5DhDbQUE=\", false, function () {\n  return [useNavigate];\n});\n_c = CartProvider;\nvar _c;\n$RefreshReg$(_c, \"CartProvider\");", "map": {"version": 3, "names": ["createContext", "useState", "useContext", "useNavigate", "httpService", "UserContext", "CURRENCY", "jsxDEV", "_jsxDEV", "CartContext", "CartProvider", "_ref", "_s", "children", "error", "setError", "productsInCart", "setProductsInCart", "localStorage", "getItem", "JSON", "parse", "shippingAddress", "setS<PERSON><PERSON><PERSON><PERSON><PERSON>", "paymentMethod", "setPaymentMethod", "navigate", "logout", "addItemToCart", "id", "qty", "item", "find", "prod", "Number", "updateItemQty", "data", "get", "product", "name", "image", "price", "countInStock", "setItem", "stringify", "ex", "message", "updatedProductsInCart", "map", "removeFromCart", "filter", "updateShippingAddress", "address", "city", "postalCode", "country", "newShippingAddress", "updatePaymentMethod", "method", "totalItemsPrice", "reduce", "acc", "toFixed", "shippingPrice", "REDUCED_SHIPPING_THRESHOLD", "FREE_SHIPPING_THRESHOLD", "FREE_SHIPPING", "REDUCED_SHIPPING", "STANDARD_SHIPPING", "taxPrice", "totalPrice", "placeOrder", "post", "orderItems", "itemsPrice", "console", "log", "removeItem", "response", "status", "contextData", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/context/cartContext.js"], "sourcesContent": ["import { createContext, useState, useContext } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport httpService from \"../services/httpService\";\nimport UserContext from './userContext';\nimport { CURRENCY } from \"../utils/currency\";\n\nconst CartContext = createContext();\n\nexport default CartContext;\n\nexport const CartProvider = ({ children }) => {\n  const [error, setError] = useState(\"\");\n  let [productsInCart, setProductsInCart] = useState(\n    localStorage.getItem(\"cartItems\")\n      ? JSON.parse(localStorage.getItem(\"cartItems\"))\n      : []\n  );\n  const [shippingAddress, setShippingAddress] = useState(\n    localStorage.getItem(\"shippingAddress\")\n      ? JSON.parse(localStorage.getItem(\"shippingAddress\"))\n      : {}\n  );\n  const [paymentMethod, setPaymentMethod] = useState(\n    localStorage.getItem(\"paymentMethod\")\n      ? localStorage.getItem(\"paymentMethod\")\n      : \"Stripe\"\n  );\n  const navigate = useNavigate();\n  const {logout} = useContext(UserContext);\n\n  const addItemToCart = async (id, qty) => {\n    const item = productsInCart.find((prod) => prod.id === Number(id));\n\n    if (item) {\n      updateItemQty(id, qty);\n      return;\n    }\n\n    try {\n      const { data } = await httpService.get(`/api/products/${id}/`);\n      const product = {\n        id: data.id,\n        name: data.name,\n        qty: qty,\n        image: data.image,\n        price: data.price,\n        countInStock: data.countInStock,\n      };\n\n      localStorage.setItem(\n        \"cartItems\",\n        JSON.stringify([...productsInCart, product])\n      );\n      setProductsInCart([...productsInCart, product]);\n    } catch (ex) {\n      setError(ex.message);\n    }\n  };\n\n  const updateItemQty = (id, qty) => {\n    const item = productsInCart.find((prod) => prod.id === Number(id));\n\n    if (item.qty == Number(qty)) return;\n\n    const product = { ...item };\n    product.qty = Number(qty);\n\n    const updatedProductsInCart = productsInCart.map((prod) =>\n      prod.id == product.id ? product : prod\n    );\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedProductsInCart));\n    setProductsInCart(updatedProductsInCart);\n  };\n\n  const removeFromCart = (id) => {\n    const updatedProductsInCart = productsInCart.filter(\n      (prod) => prod.id !== Number(id)\n    );\n\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedProductsInCart));\n    setProductsInCart(updatedProductsInCart);\n  };\n\n  const updateShippingAddress = (address, city, postalCode, country) => {\n    const newShippingAddress = {\n      address,\n      city,\n      postalCode,\n      country,\n    };\n\n    setShippingAddress(newShippingAddress);\n    localStorage.setItem(\"shippingAddress\", JSON.stringify(newShippingAddress));\n  };\n\n  const updatePaymentMethod = (method) => {\n    setPaymentMethod(method);\n    localStorage.setItem(\"paymentMethod\", method);\n  };\n\n  const totalItemsPrice = Number(\n    productsInCart\n      .reduce((acc, prod) => acc + prod.qty * prod.price, 0)\n      .toFixed(2)\n  );\n  const shippingPrice = totalItemsPrice > CURRENCY.REDUCED_SHIPPING_THRESHOLD ?\n    (totalItemsPrice >= CURRENCY.FREE_SHIPPING_THRESHOLD ? CURRENCY.FREE_SHIPPING : CURRENCY.REDUCED_SHIPPING) :\n    CURRENCY.STANDARD_SHIPPING;\n  const taxPrice = Number((0.05 * totalItemsPrice).toFixed(2));\n  const totalPrice = totalItemsPrice + shippingPrice + taxPrice;\n\n  const placeOrder = async () => {\n    try {\n      const { data } = await httpService.post(\"/api/placeorder/\", {\n        orderItems: productsInCart,\n        shippingAddress,\n        paymentMethod,\n        itemsPrice: totalItemsPrice,\n        taxPrice,\n        shippingPrice,\n        totalPrice,\n      });\n      console.log(data);\n      setProductsInCart([]);\n      localStorage.removeItem(\"cartItems\");\n      navigate(`/orders/${data.id}`);\n    } catch (ex) {\n      if (ex.response && ex.response.status == 403) logout();\n      console.log(ex.response);\n    }\n  };\n\n  const contextData = {\n    error,\n    productsInCart,\n    addItemToCart,\n    updateItemQty,\n    removeFromCart,\n    shippingAddress,\n    updateShippingAddress,\n    paymentMethod,\n    totalItemsPrice,\n    shippingPrice,\n    taxPrice,\n    totalPrice,\n    updatePaymentMethod,\n    placeOrder,\n  };\n\n  return (\n    <CartContext.Provider value={contextData}>{children}</CartContext.Provider>\n  );\n};\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAC3D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,QAAQ,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,WAAW,gBAAGT,aAAa,EAAE;AAEnC,eAAeS,WAAW;AAE1B,OAAO,MAAMC,YAAY,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACvC,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,IAAI,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAChDiB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,GAC7BC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,GAC7C,EAAE,CACP;EACD,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CACpDiB,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,GACnCC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAAC,GACnD,CAAC,CAAC,CACP;EACD,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAChDiB,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,GACjCD,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,GACrC,QAAQ,CACb;EACD,MAAMO,QAAQ,GAAGvB,WAAW,EAAE;EAC9B,MAAM;IAACwB;EAAM,CAAC,GAAGzB,UAAU,CAACG,WAAW,CAAC;EAExC,MAAMuB,aAAa,GAAG,MAAAA,CAAOC,EAAE,EAAEC,GAAG,KAAK;IACvC,MAAMC,IAAI,GAAGf,cAAc,CAACgB,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKK,MAAM,CAACL,EAAE,CAAC,CAAC;IAElE,IAAIE,IAAI,EAAE;MACRI,aAAa,CAACN,EAAE,EAAEC,GAAG,CAAC;MACtB;IACF;IAEA,IAAI;MACF,MAAM;QAAEM;MAAK,CAAC,GAAG,MAAMhC,WAAW,CAACiC,GAAG,CAAE,iBAAgBR,EAAG,GAAE,CAAC;MAC9D,MAAMS,OAAO,GAAG;QACdT,EAAE,EAAEO,IAAI,CAACP,EAAE;QACXU,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfT,GAAG,EAAEA,GAAG;QACRU,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,KAAK,EAAEL,IAAI,CAACK,KAAK;QACjBC,YAAY,EAAEN,IAAI,CAACM;MACrB,CAAC;MAEDxB,YAAY,CAACyB,OAAO,CAClB,WAAW,EACXvB,IAAI,CAACwB,SAAS,CAAC,CAAC,GAAG5B,cAAc,EAAEsB,OAAO,CAAC,CAAC,CAC7C;MACDrB,iBAAiB,CAAC,CAAC,GAAGD,cAAc,EAAEsB,OAAO,CAAC,CAAC;IACjD,CAAC,CAAC,OAAOO,EAAE,EAAE;MACX9B,QAAQ,CAAC8B,EAAE,CAACC,OAAO,CAAC;IACtB;EACF,CAAC;EAED,MAAMX,aAAa,GAAGA,CAACN,EAAE,EAAEC,GAAG,KAAK;IACjC,MAAMC,IAAI,GAAGf,cAAc,CAACgB,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKK,MAAM,CAACL,EAAE,CAAC,CAAC;IAElE,IAAIE,IAAI,CAACD,GAAG,IAAII,MAAM,CAACJ,GAAG,CAAC,EAAE;IAE7B,MAAMQ,OAAO,GAAG;MAAE,GAAGP;IAAK,CAAC;IAC3BO,OAAO,CAACR,GAAG,GAAGI,MAAM,CAACJ,GAAG,CAAC;IAEzB,MAAMiB,qBAAqB,GAAG/B,cAAc,CAACgC,GAAG,CAAEf,IAAI,IACpDA,IAAI,CAACJ,EAAE,IAAIS,OAAO,CAACT,EAAE,GAAGS,OAAO,GAAGL,IAAI,CACvC;IACDf,YAAY,CAACyB,OAAO,CAAC,WAAW,EAAEvB,IAAI,CAACwB,SAAS,CAACG,qBAAqB,CAAC,CAAC;IACxE9B,iBAAiB,CAAC8B,qBAAqB,CAAC;EAC1C,CAAC;EAED,MAAME,cAAc,GAAIpB,EAAE,IAAK;IAC7B,MAAMkB,qBAAqB,GAAG/B,cAAc,CAACkC,MAAM,CAChDjB,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKK,MAAM,CAACL,EAAE,CAAC,CACjC;IAEDX,YAAY,CAACyB,OAAO,CAAC,WAAW,EAAEvB,IAAI,CAACwB,SAAS,CAACG,qBAAqB,CAAC,CAAC;IACxE9B,iBAAiB,CAAC8B,qBAAqB,CAAC;EAC1C,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEC,UAAU,EAAEC,OAAO,KAAK;IACpE,MAAMC,kBAAkB,GAAG;MACzBJ,OAAO;MACPC,IAAI;MACJC,UAAU;MACVC;IACF,CAAC;IAEDhC,kBAAkB,CAACiC,kBAAkB,CAAC;IACtCtC,YAAY,CAACyB,OAAO,CAAC,iBAAiB,EAAEvB,IAAI,CAACwB,SAAS,CAACY,kBAAkB,CAAC,CAAC;EAC7E,CAAC;EAED,MAAMC,mBAAmB,GAAIC,MAAM,IAAK;IACtCjC,gBAAgB,CAACiC,MAAM,CAAC;IACxBxC,YAAY,CAACyB,OAAO,CAAC,eAAe,EAAEe,MAAM,CAAC;EAC/C,CAAC;EAED,MAAMC,eAAe,GAAGzB,MAAM,CAC5BlB,cAAc,CACX4C,MAAM,CAAC,CAACC,GAAG,EAAE5B,IAAI,KAAK4B,GAAG,GAAG5B,IAAI,CAACH,GAAG,GAAGG,IAAI,CAACQ,KAAK,EAAE,CAAC,CAAC,CACrDqB,OAAO,CAAC,CAAC,CAAC,CACd;EACD,MAAMC,aAAa,GAAGJ,eAAe,GAAGrD,QAAQ,CAAC0D,0BAA0B,GACxEL,eAAe,IAAIrD,QAAQ,CAAC2D,uBAAuB,GAAG3D,QAAQ,CAAC4D,aAAa,GAAG5D,QAAQ,CAAC6D,gBAAgB,GACzG7D,QAAQ,CAAC8D,iBAAiB;EAC5B,MAAMC,QAAQ,GAAGnC,MAAM,CAAC,CAAC,IAAI,GAAGyB,eAAe,EAAEG,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMQ,UAAU,GAAGX,eAAe,GAAGI,aAAa,GAAGM,QAAQ;EAE7D,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAM;QAAEnC;MAAK,CAAC,GAAG,MAAMhC,WAAW,CAACoE,IAAI,CAAC,kBAAkB,EAAE;QAC1DC,UAAU,EAAEzD,cAAc;QAC1BM,eAAe;QACfE,aAAa;QACbkD,UAAU,EAAEf,eAAe;QAC3BU,QAAQ;QACRN,aAAa;QACbO;MACF,CAAC,CAAC;MACFK,OAAO,CAACC,GAAG,CAACxC,IAAI,CAAC;MACjBnB,iBAAiB,CAAC,EAAE,CAAC;MACrBC,YAAY,CAAC2D,UAAU,CAAC,WAAW,CAAC;MACpCnD,QAAQ,CAAE,WAAUU,IAAI,CAACP,EAAG,EAAC,CAAC;IAChC,CAAC,CAAC,OAAOgB,EAAE,EAAE;MACX,IAAIA,EAAE,CAACiC,QAAQ,IAAIjC,EAAE,CAACiC,QAAQ,CAACC,MAAM,IAAI,GAAG,EAAEpD,MAAM,EAAE;MACtDgD,OAAO,CAACC,GAAG,CAAC/B,EAAE,CAACiC,QAAQ,CAAC;IAC1B;EACF,CAAC;EAED,MAAME,WAAW,GAAG;IAClBlE,KAAK;IACLE,cAAc;IACdY,aAAa;IACbO,aAAa;IACbc,cAAc;IACd3B,eAAe;IACf6B,qBAAqB;IACrB3B,aAAa;IACbmC,eAAe;IACfI,aAAa;IACbM,QAAQ;IACRC,UAAU;IACVb,mBAAmB;IACnBc;EACF,CAAC;EAED,oBACE/D,OAAA,CAACC,WAAW,CAACwE,QAAQ;IAACC,KAAK,EAAEF,WAAY;IAAAnE,QAAA,EAAEA;EAAQ;IAAAsE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAwB;AAE/E,CAAC;AAAC1E,EAAA,CA9IWF,YAAY;EAAA,QAiBNP,WAAW;AAAA;AAAAoF,EAAA,GAjBjB7E,YAAY;AAAA,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}