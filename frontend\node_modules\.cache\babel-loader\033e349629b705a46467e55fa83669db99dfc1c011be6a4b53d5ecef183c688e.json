{"ast": null, "code": "import axios from \"axios\";\n\n// Tạo instance axios với baseURL\nconst httpService = axios.create({\n  baseURL: \"/\"\n});\n\n// Thêm interceptor để tự động thêm token vào header\nhttpService.interceptors.request.use(config => {\n  const authTokens = localStorage.getItem(\"authTokens\") ? JSON.parse(localStorage.getItem(\"authTokens\")) : null;\n  if (authTokens) {\n    config.headers.Authorization = `Bearer ${authTokens.access}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nexport default httpService;", "map": {"version": 3, "names": ["axios", "httpService", "create", "baseURL", "interceptors", "request", "use", "config", "authTokens", "localStorage", "getItem", "JSON", "parse", "headers", "Authorization", "access", "error", "Promise", "reject"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/services/httpService.js"], "sourcesContent": ["import axios from \"axios\";\n\n// Tạo instance axios với baseURL\nconst httpService = axios.create({\n  baseURL: \"/\",\n});\n\n// Thêm interceptor để tự động thêm token vào header\nhttpService.interceptors.request.use(\n  (config) => {\n    const authTokens = localStorage.getItem(\"authTokens\")\n      ? JSON.parse(localStorage.getItem(\"authTokens\"))\n      : null;\n    \n    if (authTokens) {\n      config.headers.Authorization = `Bearer ${authTokens.access}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nexport default httpService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,WAAW,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC/BC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAF,WAAW,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;EACV,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GACjDC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,GAC9C,IAAI;EAER,IAAIF,UAAU,EAAE;IACdD,MAAM,CAACM,OAAO,CAACC,aAAa,GAAI,UAASN,UAAU,CAACO,MAAO,EAAC;EAC9D;EACA,OAAOR,MAAM;AACf,CAAC,EACAS,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CAAC,CACF;AAED,eAAef,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}