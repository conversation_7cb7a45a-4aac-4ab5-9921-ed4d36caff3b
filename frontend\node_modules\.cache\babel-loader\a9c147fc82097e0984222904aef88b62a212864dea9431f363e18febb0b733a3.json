{"ast": null, "code": "import axios from \"axios\";\n\n// Don't set base URL since we're using proxy in package.json\n// axios.defaults.baseURL = 'http://localhost:8000';\n\nfunction setJwt(jwt) {\n  if (jwt == undefined) {\n    delete axios.defaults.headers.common[\"Authorization\"];\n    return;\n  }\n  axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n  console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(config => {\n  var _config$method;\n  console.log('Making request:', (_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase(), config.url);\n  console.log('Headers:', config.headers);\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(response => {\n  console.log('Response received:', response.status, response.config.url);\n  return response;\n}, error => {\n  var _error$response, _error$config, _error$response2;\n  console.log('Request failed:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status, (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url);\n  console.log('Error details:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data);\n  return Promise.reject(error);\n});\nexport default {\n  get: axios.get,\n  post: axios.post,\n  put: axios.put,\n  patch: axios.patch,\n  delete: axios.delete,\n  setJwt\n};", "map": {"version": 3, "names": ["axios", "setJwt", "jwt", "undefined", "defaults", "headers", "common", "console", "log", "substring", "interceptors", "request", "use", "config", "_config$method", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "_error$response", "_error$config", "_error$response2", "data", "get", "post", "put", "patch", "delete"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/services/httpService.js"], "sourcesContent": ["import axios from \"axios\";\n\n// Don't set base URL since we're using proxy in package.json\n// axios.defaults.baseURL = 'http://localhost:8000';\n\nfunction setJwt(jwt) {\n    if (jwt == undefined) {\n        delete axios.defaults.headers.common[\"Authorization\"];\n        return;\n    }\n    axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n    console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(\n    (config) => {\n        console.log('Making request:', config.method?.toUpperCase(), config.url);\n        console.log('Headers:', config.headers);\n        return config;\n    },\n    (error) => {\n        return Promise.reject(error);\n    }\n);\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(\n    (response) => {\n        console.log('Response received:', response.status, response.config.url);\n        return response;\n    },\n    (error) => {\n        console.log('Request failed:', error.response?.status, error.config?.url);\n        console.log('Error details:', error.response?.data);\n        return Promise.reject(error);\n    }\n);\n\nexport default {\n    get:axios.get,\n    post:axios.post,\n    put:axios.put,\n    patch:axios.patch,\n    delete:axios.delete,\n    setJwt\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA;;AAEA,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,IAAIA,GAAG,IAAIC,SAAS,EAAE;IAClB,OAAOH,KAAK,CAACI,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrD;EACJ;EACAN,KAAK,CAACI,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAI,OAAMJ,GAAI,EAAC;EAC7DK,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAG,OAAMN,GAAG,CAACO,SAAS,CAAC,CAAC,EAAE,EAAE,CAAE,KAAI,CAAC;AACnE;;AAEA;AACAT,KAAK,CAACU,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACRP,OAAO,CAACC,GAAG,CAAC,iBAAiB,GAAAM,cAAA,GAAED,MAAM,CAACE,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,EAAE,EAAEH,MAAM,CAACI,GAAG,CAAC;EACxEV,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEK,MAAM,CAACR,OAAO,CAAC;EACvC,OAAOQ,MAAM;AACjB,CAAC,EACAK,KAAK,IAAK;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CACJ;;AAED;AACAlB,KAAK,CAACU,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACVd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACR,MAAM,CAACI,GAAG,CAAC;EACvE,OAAOI,QAAQ;AACnB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,aAAA,EAAAC,gBAAA;EACPlB,OAAO,CAACC,GAAG,CAAC,iBAAiB,GAAAe,eAAA,GAAEL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBD,MAAM,GAAAE,aAAA,GAAEN,KAAK,CAACL,MAAM,cAAAW,aAAA,uBAAZA,aAAA,CAAcP,GAAG,CAAC;EACzEV,OAAO,CAACC,GAAG,CAAC,gBAAgB,GAAAiB,gBAAA,GAAEP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBC,IAAI,CAAC;EACnD,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CACJ;AAED,eAAe;EACXS,GAAG,EAAC3B,KAAK,CAAC2B,GAAG;EACbC,IAAI,EAAC5B,KAAK,CAAC4B,IAAI;EACfC,GAAG,EAAC7B,KAAK,CAAC6B,GAAG;EACbC,KAAK,EAAC9B,KAAK,CAAC8B,KAAK;EACjBC,MAAM,EAAC/B,KAAK,CAAC+B,MAAM;EACnB9B;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}