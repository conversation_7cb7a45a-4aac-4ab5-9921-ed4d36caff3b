[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "47", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "48"}, {"size": 649, "mtime": 1750943544495, "results": "49", "hashOfConfig": "50"}, {"size": 362, "mtime": 1750943544502, "results": "51", "hashOfConfig": "50"}, {"size": 5092, "mtime": 1750995236291, "results": "52", "hashOfConfig": "50"}, {"size": 1585, "mtime": 1750943544494, "results": "53", "hashOfConfig": "50"}, {"size": 4116, "mtime": 1750943544494, "results": "54", "hashOfConfig": "50"}, {"size": 4438, "mtime": 1750993170933, "results": "55", "hashOfConfig": "50"}, {"size": 1795, "mtime": 1750943544489, "results": "56", "hashOfConfig": "50"}, {"size": 3086, "mtime": 1750993154771, "results": "57", "hashOfConfig": "50"}, {"size": 562, "mtime": 1750943544488, "results": "58", "hashOfConfig": "50"}, {"size": 1986, "mtime": 1750996901833, "results": "59", "hashOfConfig": "50"}, {"size": 4041, "mtime": 1750943544496, "results": "60", "hashOfConfig": "50"}, {"size": 4556, "mtime": 1750943544500, "results": "61", "hashOfConfig": "50"}, {"size": 3166, "mtime": 1750943544501, "results": "62", "hashOfConfig": "50"}, {"size": 3374, "mtime": 1750943544501, "results": "63", "hashOfConfig": "50"}, {"size": 2746, "mtime": 1750943544500, "results": "64", "hashOfConfig": "50"}, {"size": 557, "mtime": 1750943544498, "results": "65", "hashOfConfig": "50"}, {"size": 1873, "mtime": 1750945277042, "results": "66", "hashOfConfig": "50"}, {"size": 5003, "mtime": 1750943544499, "results": "67", "hashOfConfig": "50"}, {"size": 1876, "mtime": 1750943544499, "results": "68", "hashOfConfig": "50"}, {"size": 6146, "mtime": 1750943544499, "results": "69", "hashOfConfig": "50"}, {"size": 4610, "mtime": 1750997032701, "results": "70", "hashOfConfig": "50"}, {"size": 392, "mtime": 1750943544489, "results": "71", "hashOfConfig": "50"}, {"size": 988, "mtime": 1750943544487, "results": "72", "hashOfConfig": "50"}, {"size": 1572, "mtime": 1751033967838, "results": "73", "hashOfConfig": "50"}, {"size": 1221, "mtime": 1750943544493, "results": "74", "hashOfConfig": "50"}, {"size": 228, "mtime": 1750943544490, "results": "75", "hashOfConfig": "50"}, {"size": 1101, "mtime": 1750943544491, "results": "76", "hashOfConfig": "50"}, {"size": 737, "mtime": 1750943544487, "results": "77", "hashOfConfig": "50"}, {"size": 901, "mtime": 1750943544491, "results": "78", "hashOfConfig": "50"}, {"size": 3665, "mtime": 1750943544493, "results": "79", "hashOfConfig": "50"}, {"size": 372, "mtime": 1750943544489, "results": "80", "hashOfConfig": "50"}, {"size": 2548, "mtime": 1750943544493, "results": "81", "hashOfConfig": "50"}, {"size": 2457, "mtime": 1750996243341, "results": "82", "hashOfConfig": "50"}, {"size": 1519, "mtime": 1750943544488, "results": "83", "hashOfConfig": "50"}, {"size": 639, "mtime": 1750943544492, "results": "84", "hashOfConfig": "50"}, {"size": 1826, "mtime": 1750943544491, "results": "85", "hashOfConfig": "50"}, {"size": 11285, "mtime": 1750993002543, "results": "86", "hashOfConfig": "50"}, {"size": 10483, "mtime": 1750993066396, "results": "87", "hashOfConfig": "50"}, {"size": 7625, "mtime": 1751031499765, "results": "88", "hashOfConfig": "50"}, {"size": 445, "mtime": 1750994572623, "results": "89", "hashOfConfig": "50"}, {"size": 3134, "mtime": 1750997224891, "results": "90", "hashOfConfig": "50"}, {"size": 3307, "mtime": 1750993684234, "results": "91", "hashOfConfig": "50"}, {"size": 8263, "mtime": 1751031465839, "results": "92", "hashOfConfig": "50"}, {"size": 551, "mtime": 1750993273555, "results": "93", "hashOfConfig": "50"}, {"size": 8081, "mtime": 1751031022948, "results": "94", "hashOfConfig": "50"}, {"size": 14883, "mtime": 1751033321582, "results": "95", "hashOfConfig": "50"}, {"size": 8445, "mtime": 1751031717046, "results": "96", "hashOfConfig": "50"}, {"size": 702, "mtime": 1750996844606, "results": "97", "hashOfConfig": "50"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "170"}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["243"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["244"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["245", "246", "247"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["248", "249", "250", "251", "252", "253", "254", "255", "256"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["257", "258"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["259"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["260", "261"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["262", "263"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["264", "265"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["266"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["267", "268"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["269"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["270"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["271", "272", "273"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["274"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["275", "276", "277", "278"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["279", "280", "281", "282", "283", "284", "285", "286", "287"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["288", "289"], [], "import axios from \"axios\";\n\n// Set base URL directly to Django backend\naxios.defaults.baseURL = 'http://localhost:8000';\n\n// Set default headers for JSON\naxios.defaults.headers.common['Content-Type'] = 'application/json';\naxios.defaults.headers.put['Content-Type'] = 'application/json';\naxios.defaults.headers.post['Content-Type'] = 'application/json';\naxios.defaults.headers.patch['Content-Type'] = 'application/json';\n\nfunction setJwt(jwt) {\n    if (jwt == undefined) {\n        delete axios.defaults.headers.common[\"Authorization\"];\n        return;\n    }\n    axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n    console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(\n    (config) => {\n        console.log('Making request:', config.method?.toUpperCase(), config.url);\n        console.log('Headers:', config.headers);\n        return config;\n    },\n    (error) => {\n        return Promise.reject(error);\n    }\n);\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(\n    (response) => {\n        console.log('Response received:', response.status, response.config.url);\n        return response;\n    },\n    (error) => {\n        console.log('Request failed:', error.response?.status, error.config?.url);\n        console.log('Error details:', error.response?.data);\n        return Promise.reject(error);\n    }\n);\n\nexport default {\n    get:axios.get,\n    post:axios.post,\n    put:axios.put,\n    patch:axios.patch,\n    delete:axios.delete,\n    setJwt\n};", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["290"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["291", "292"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["293"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["294", "295", "296"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["297"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["298"], [], {"ruleId": "299", "severity": 1, "message": "300", "line": 41, "column": 3, "nodeType": "301", "endLine": 41, "endColumn": 12, "suggestions": "302"}, {"ruleId": "303", "severity": 1, "message": "304", "line": 35, "column": 49, "nodeType": "305", "messageId": "306", "endLine": 35, "endColumn": 51}, {"ruleId": "303", "severity": 1, "message": "304", "line": 62, "column": 18, "nodeType": "305", "messageId": "306", "endLine": 62, "endColumn": 20}, {"ruleId": "303", "severity": 1, "message": "304", "line": 68, "column": 15, "nodeType": "305", "messageId": "306", "endLine": 68, "endColumn": 17}, {"ruleId": "303", "severity": 1, "message": "304", "line": 125, "column": 45, "nodeType": "305", "messageId": "306", "endLine": 125, "endColumn": 47}, {"ruleId": "307", "severity": 1, "message": "308", "line": 58, "column": 15, "nodeType": "301", "messageId": "309", "endLine": 58, "endColumn": 19}, {"ruleId": "299", "severity": 1, "message": "310", "line": 104, "column": 6, "nodeType": "311", "endLine": 104, "endColumn": 8, "suggestions": "312"}, {"ruleId": "299", "severity": 1, "message": "313", "line": 116, "column": 6, "nodeType": "311", "endLine": 116, "endColumn": 18, "suggestions": "314"}, {"ruleId": "303", "severity": 1, "message": "315", "line": 121, "column": 20, "nodeType": "305", "messageId": "306", "endLine": 121, "endColumn": 22}, {"ruleId": "303", "severity": 1, "message": "315", "line": 121, "column": 53, "nodeType": "305", "messageId": "306", "endLine": 121, "endColumn": 55}, {"ruleId": "303", "severity": 1, "message": "315", "line": 125, "column": 17, "nodeType": "305", "messageId": "306", "endLine": 125, "endColumn": 19}, {"ruleId": "303", "severity": 1, "message": "315", "line": 125, "column": 44, "nodeType": "305", "messageId": "306", "endLine": 125, "endColumn": 46}, {"ruleId": "303", "severity": 1, "message": "315", "line": 129, "column": 20, "nodeType": "305", "messageId": "306", "endLine": 129, "endColumn": 22}, {"ruleId": "303", "severity": 1, "message": "304", "line": 133, "column": 16, "nodeType": "305", "messageId": "306", "endLine": 133, "endColumn": 18}, {"ruleId": "307", "severity": 1, "message": "316", "line": 13, "column": 23, "nodeType": "301", "messageId": "309", "endLine": 13, "endColumn": 38}, {"ruleId": "299", "severity": 1, "message": "317", "line": 27, "column": 6, "nodeType": "311", "endLine": 27, "endColumn": 8, "suggestions": "318"}, {"ruleId": "319", "severity": 1, "message": "320", "line": 14, "column": 13, "nodeType": "321", "messageId": "322", "endLine": 14, "endColumn": 107, "fix": "323"}, {"ruleId": "299", "severity": 1, "message": "324", "line": 22, "column": 6, "nodeType": "311", "endLine": 22, "endColumn": 8, "suggestions": "325"}, {"ruleId": "303", "severity": 1, "message": "315", "line": 26, "column": 13, "nodeType": "305", "messageId": "306", "endLine": 26, "endColumn": 15}, {"ruleId": "307", "severity": 1, "message": "326", "line": 1, "column": 29, "nodeType": "301", "messageId": "309", "endLine": 1, "endColumn": 38}, {"ruleId": "303", "severity": 1, "message": "315", "line": 25, "column": 13, "nodeType": "305", "messageId": "306", "endLine": 25, "endColumn": 15}, {"ruleId": "299", "severity": 1, "message": "327", "line": 34, "column": 6, "nodeType": "311", "endLine": 34, "endColumn": 8, "suggestions": "328"}, {"ruleId": "303", "severity": 1, "message": "315", "line": 43, "column": 13, "nodeType": "305", "messageId": "306", "endLine": 43, "endColumn": 15}, {"ruleId": "299", "severity": 1, "message": "329", "line": 31, "column": 6, "nodeType": "311", "endLine": 31, "endColumn": 8, "suggestions": "330"}, {"ruleId": "331", "severity": 1, "message": "332", "line": 18, "column": 28, "nodeType": "301", "messageId": "333", "endLine": 18, "endColumn": 36}, {"ruleId": "299", "severity": 1, "message": "317", "line": 22, "column": 6, "nodeType": "311", "endLine": 22, "endColumn": 8, "suggestions": "334"}, {"ruleId": "299", "severity": 1, "message": "329", "line": 22, "column": 6, "nodeType": "311", "endLine": 22, "endColumn": 8, "suggestions": "335"}, {"ruleId": "299", "severity": 1, "message": "336", "line": 15, "column": 6, "nodeType": "311", "endLine": 15, "endColumn": 8, "suggestions": "337"}, {"ruleId": "307", "severity": 1, "message": "338", "line": 29, "column": 11, "nodeType": "301", "messageId": "309", "endLine": 29, "endColumn": 13}, {"ruleId": "303", "severity": 1, "message": "304", "line": 59, "column": 38, "nodeType": "305", "messageId": "306", "endLine": 59, "endColumn": 40}, {"ruleId": "303", "severity": 1, "message": "304", "line": 129, "column": 53, "nodeType": "305", "messageId": "306", "endLine": 129, "endColumn": 55}, {"ruleId": "303", "severity": 1, "message": "304", "line": 37, "column": 33, "nodeType": "305", "messageId": "306", "endLine": 37, "endColumn": 35}, {"ruleId": "303", "severity": 1, "message": "304", "line": 26, "column": 47, "nodeType": "305", "messageId": "306", "endLine": 26, "endColumn": 49}, {"ruleId": "303", "severity": 1, "message": "304", "line": 27, "column": 47, "nodeType": "305", "messageId": "306", "endLine": 27, "endColumn": 49}, {"ruleId": "299", "severity": 1, "message": "339", "line": 34, "column": 6, "nodeType": "311", "endLine": 34, "endColumn": 8, "suggestions": "340"}, {"ruleId": "303", "severity": 1, "message": "315", "line": 40, "column": 17, "nodeType": "305", "messageId": "306", "endLine": 40, "endColumn": 19}, {"ruleId": "307", "severity": 1, "message": "316", "line": 18, "column": 24, "nodeType": "301", "messageId": "309", "endLine": 18, "endColumn": 39}, {"ruleId": "299", "severity": 1, "message": "341", "line": 43, "column": 6, "nodeType": "311", "endLine": 43, "endColumn": 8, "suggestions": "342"}, {"ruleId": "303", "severity": 1, "message": "315", "line": 49, "column": 21, "nodeType": "305", "messageId": "306", "endLine": 49, "endColumn": 23}, {"ruleId": "303", "severity": 1, "message": "304", "line": 51, "column": 34, "nodeType": "305", "messageId": "306", "endLine": 51, "endColumn": 36}, {"ruleId": "303", "severity": 1, "message": "315", "line": 55, "column": 24, "nodeType": "305", "messageId": "306", "endLine": 55, "endColumn": 26}, {"ruleId": "303", "severity": 1, "message": "304", "line": 57, "column": 37, "nodeType": "305", "messageId": "306", "endLine": 57, "endColumn": 39}, {"ruleId": "303", "severity": 1, "message": "315", "line": 63, "column": 13, "nodeType": "305", "messageId": "306", "endLine": 63, "endColumn": 15}, {"ruleId": "303", "severity": 1, "message": "315", "line": 63, "column": 35, "nodeType": "305", "messageId": "306", "endLine": 63, "endColumn": 37}, {"ruleId": "303", "severity": 1, "message": "315", "line": 66, "column": 20, "nodeType": "305", "messageId": "306", "endLine": 66, "endColumn": 22}, {"ruleId": "303", "severity": 1, "message": "304", "line": 13, "column": 13, "nodeType": "305", "messageId": "306", "endLine": 13, "endColumn": 15}, {"ruleId": "343", "severity": 1, "message": "344", "line": 46, "column": 1, "nodeType": "345", "endLine": 53, "endColumn": 3}, {"ruleId": "307", "severity": 1, "message": "326", "line": 1, "column": 17, "nodeType": "301", "messageId": "309", "endLine": 1, "endColumn": 26}, {"ruleId": "303", "severity": 1, "message": "304", "line": 90, "column": 34, "nodeType": "305", "messageId": "306", "endLine": 90, "endColumn": 36}, {"ruleId": "303", "severity": 1, "message": "304", "line": 90, "column": 51, "nodeType": "305", "messageId": "306", "endLine": 90, "endColumn": 53}, {"ruleId": "303", "severity": 1, "message": "304", "line": 21, "column": 47, "nodeType": "305", "messageId": "306", "endLine": 21, "endColumn": 49}, {"ruleId": "307", "severity": 1, "message": "326", "line": 1, "column": 17, "nodeType": "301", "messageId": "309", "endLine": 1, "endColumn": 26}, {"ruleId": "307", "severity": 1, "message": "346", "line": 8, "column": 8, "nodeType": "301", "messageId": "309", "endLine": 8, "endColumn": 19}, {"ruleId": "307", "severity": 1, "message": "347", "line": 14, "column": 10, "nodeType": "301", "messageId": "309", "endLine": 14, "endColumn": 15}, {"ruleId": "307", "severity": 1, "message": "348", "line": 9, "column": 10, "nodeType": "301", "messageId": "309", "endLine": 9, "endColumn": 18}, {"ruleId": "307", "severity": 1, "message": "349", "line": 1, "column": 8, "nodeType": "301", "messageId": "309", "endLine": 1, "endColumn": 13}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["350"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["351"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["352"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["353"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "354", "text": "355"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["356"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["357"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["358"], "no-const-assign", "'redirect' is constant.", "const", ["359"], ["360"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["361"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["362"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["363"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'products' is assigned a value but never used.", "'React' is defined but never used.", {"desc": "364", "fix": "365"}, {"desc": "366", "fix": "367"}, {"desc": "366", "fix": "368"}, {"desc": "369", "fix": "370"}, [447, 447], " rel=\"noreferrer\"", {"desc": "371", "fix": "372"}, {"desc": "373", "fix": "374"}, {"desc": "375", "fix": "376"}, {"desc": "369", "fix": "377"}, {"desc": "375", "fix": "378"}, {"desc": "379", "fix": "380"}, {"desc": "381", "fix": "382"}, {"desc": "383", "fix": "384"}, "Add dependencies array: [keywordParam]", {"range": "385", "text": "386"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "387", "text": "388"}, {"range": "389", "text": "388"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "390", "text": "391"}, "Update the dependencies array to be: [loadProducts]", {"range": "392", "text": "393"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "396", "text": "397"}, {"range": "398", "text": "391"}, {"range": "399", "text": "397"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "400", "text": "401"}, "Update the dependencies array to be: [id, logout]", {"range": "402", "text": "403"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "404", "text": "405"}, [1834, 1834], ", [keywordParam]", [2796, 2798], "[authTokens, refresh]", [3172, 3184], [956, 958], "[navigate, redirect, userInfo]", [839, 841], "[loadProducts]", [1011, 1013], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1286, 1288], "[id, logout]", [1488, 1490], "[brandParam, categoryParam, loadProducts]"]