[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "47", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "48"}, {"size": 649, "mtime": 1750943544495, "results": "49", "hashOfConfig": "50"}, {"size": 362, "mtime": 1750943544502, "results": "51", "hashOfConfig": "50"}, {"size": 5092, "mtime": 1750995236291, "results": "52", "hashOfConfig": "50"}, {"size": 1585, "mtime": 1750943544494, "results": "53", "hashOfConfig": "50"}, {"size": 4116, "mtime": 1750943544494, "results": "54", "hashOfConfig": "50"}, {"size": 4438, "mtime": 1750993170933, "results": "55", "hashOfConfig": "50"}, {"size": 1795, "mtime": 1750943544489, "results": "56", "hashOfConfig": "50"}, {"size": 3086, "mtime": 1750993154771, "results": "57", "hashOfConfig": "50"}, {"size": 562, "mtime": 1750943544488, "results": "58", "hashOfConfig": "50"}, {"size": 1986, "mtime": 1750996901833, "results": "59", "hashOfConfig": "50"}, {"size": 4041, "mtime": 1750943544496, "results": "60", "hashOfConfig": "50"}, {"size": 4556, "mtime": 1750943544500, "results": "61", "hashOfConfig": "50"}, {"size": 3166, "mtime": 1750943544501, "results": "62", "hashOfConfig": "50"}, {"size": 3374, "mtime": 1750943544501, "results": "63", "hashOfConfig": "50"}, {"size": 2746, "mtime": 1750943544500, "results": "64", "hashOfConfig": "50"}, {"size": 557, "mtime": 1750943544498, "results": "65", "hashOfConfig": "50"}, {"size": 1873, "mtime": 1750945277042, "results": "66", "hashOfConfig": "50"}, {"size": 5003, "mtime": 1750943544499, "results": "67", "hashOfConfig": "50"}, {"size": 1876, "mtime": 1750943544499, "results": "68", "hashOfConfig": "50"}, {"size": 6146, "mtime": 1750943544499, "results": "69", "hashOfConfig": "50"}, {"size": 4610, "mtime": 1750997032701, "results": "70", "hashOfConfig": "50"}, {"size": 392, "mtime": 1750943544489, "results": "71", "hashOfConfig": "50"}, {"size": 988, "mtime": 1750943544487, "results": "72", "hashOfConfig": "50"}, {"size": 1572, "mtime": 1751033967838, "results": "73", "hashOfConfig": "50"}, {"size": 1221, "mtime": 1750943544493, "results": "74", "hashOfConfig": "50"}, {"size": 228, "mtime": 1750943544490, "results": "75", "hashOfConfig": "50"}, {"size": 1101, "mtime": 1750943544491, "results": "76", "hashOfConfig": "50"}, {"size": 737, "mtime": 1750943544487, "results": "77", "hashOfConfig": "50"}, {"size": 901, "mtime": 1750943544491, "results": "78", "hashOfConfig": "50"}, {"size": 3665, "mtime": 1750943544493, "results": "79", "hashOfConfig": "50"}, {"size": 372, "mtime": 1750943544489, "results": "80", "hashOfConfig": "50"}, {"size": 2548, "mtime": 1750943544493, "results": "81", "hashOfConfig": "50"}, {"size": 2457, "mtime": 1750996243341, "results": "82", "hashOfConfig": "50"}, {"size": 1519, "mtime": 1750943544488, "results": "83", "hashOfConfig": "50"}, {"size": 639, "mtime": 1750943544492, "results": "84", "hashOfConfig": "50"}, {"size": 1826, "mtime": 1750943544491, "results": "85", "hashOfConfig": "50"}, {"size": 11285, "mtime": 1750993002543, "results": "86", "hashOfConfig": "50"}, {"size": 10483, "mtime": 1750993066396, "results": "87", "hashOfConfig": "50"}, {"size": 7625, "mtime": 1751031499765, "results": "88", "hashOfConfig": "50"}, {"size": 445, "mtime": 1750994572623, "results": "89", "hashOfConfig": "50"}, {"size": 3134, "mtime": 1750997224891, "results": "90", "hashOfConfig": "50"}, {"size": 3307, "mtime": 1750993684234, "results": "91", "hashOfConfig": "50"}, {"size": 8263, "mtime": 1751031465839, "results": "92", "hashOfConfig": "50"}, {"size": 551, "mtime": 1750993273555, "results": "93", "hashOfConfig": "50"}, {"size": 8081, "mtime": 1751031022948, "results": "94", "hashOfConfig": "50"}, {"size": 13490, "mtime": 1751034928574, "results": "95", "hashOfConfig": "50"}, {"size": 8445, "mtime": 1751031717046, "results": "96", "hashOfConfig": "50"}, {"size": 702, "mtime": 1750996844606, "results": "97", "hashOfConfig": "50"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "170", "usedDeprecatedRules": "171"}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["244"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["245"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["246", "247", "248"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["249", "250", "251", "252", "253", "254", "255", "256", "257"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["258", "259"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["260"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["261", "262"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["263", "264"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["265", "266"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["267"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["268", "269"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["270"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["271"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["272", "273", "274"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["275"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["276", "277", "278", "279"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["280", "281", "282", "283", "284", "285", "286", "287", "288"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["289", "290"], [], "import axios from \"axios\";\n\n// Set base URL directly to Django backend\naxios.defaults.baseURL = 'http://localhost:8000';\n\n// Set default headers for JSON\naxios.defaults.headers.common['Content-Type'] = 'application/json';\naxios.defaults.headers.put['Content-Type'] = 'application/json';\naxios.defaults.headers.post['Content-Type'] = 'application/json';\naxios.defaults.headers.patch['Content-Type'] = 'application/json';\n\nfunction setJwt(jwt) {\n    if (jwt == undefined) {\n        delete axios.defaults.headers.common[\"Authorization\"];\n        return;\n    }\n    axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n    console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(\n    (config) => {\n        console.log('Making request:', config.method?.toUpperCase(), config.url);\n        console.log('Headers:', config.headers);\n        return config;\n    },\n    (error) => {\n        return Promise.reject(error);\n    }\n);\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(\n    (response) => {\n        console.log('Response received:', response.status, response.config.url);\n        return response;\n    },\n    (error) => {\n        console.log('Request failed:', error.response?.status, error.config?.url);\n        console.log('Error details:', error.response?.data);\n        return Promise.reject(error);\n    }\n);\n\nexport default {\n    get:axios.get,\n    post:axios.post,\n    put:axios.put,\n    patch:axios.patch,\n    delete:axios.delete,\n    setJwt\n};", [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["291"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["292", "293"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["294"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["295", "296", "297"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["298"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["299"], [], {"ruleId": "300", "severity": 1, "message": "301", "line": 41, "column": 3, "nodeType": "302", "endLine": 41, "endColumn": 12, "suggestions": "303"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 35, "column": 49, "nodeType": "306", "messageId": "307", "endLine": 35, "endColumn": 51}, {"ruleId": "304", "severity": 1, "message": "305", "line": 62, "column": 18, "nodeType": "306", "messageId": "307", "endLine": 62, "endColumn": 20}, {"ruleId": "304", "severity": 1, "message": "305", "line": 68, "column": 15, "nodeType": "306", "messageId": "307", "endLine": 68, "endColumn": 17}, {"ruleId": "304", "severity": 1, "message": "305", "line": 125, "column": 45, "nodeType": "306", "messageId": "307", "endLine": 125, "endColumn": 47}, {"ruleId": "308", "severity": 1, "message": "309", "line": 58, "column": 15, "nodeType": "302", "messageId": "310", "endLine": 58, "endColumn": 19}, {"ruleId": "300", "severity": 1, "message": "311", "line": 104, "column": 6, "nodeType": "312", "endLine": 104, "endColumn": 8, "suggestions": "313"}, {"ruleId": "300", "severity": 1, "message": "314", "line": 116, "column": 6, "nodeType": "312", "endLine": 116, "endColumn": 18, "suggestions": "315"}, {"ruleId": "304", "severity": 1, "message": "316", "line": 121, "column": 20, "nodeType": "306", "messageId": "307", "endLine": 121, "endColumn": 22}, {"ruleId": "304", "severity": 1, "message": "316", "line": 121, "column": 53, "nodeType": "306", "messageId": "307", "endLine": 121, "endColumn": 55}, {"ruleId": "304", "severity": 1, "message": "316", "line": 125, "column": 17, "nodeType": "306", "messageId": "307", "endLine": 125, "endColumn": 19}, {"ruleId": "304", "severity": 1, "message": "316", "line": 125, "column": 44, "nodeType": "306", "messageId": "307", "endLine": 125, "endColumn": 46}, {"ruleId": "304", "severity": 1, "message": "316", "line": 129, "column": 20, "nodeType": "306", "messageId": "307", "endLine": 129, "endColumn": 22}, {"ruleId": "304", "severity": 1, "message": "305", "line": 133, "column": 16, "nodeType": "306", "messageId": "307", "endLine": 133, "endColumn": 18}, {"ruleId": "308", "severity": 1, "message": "317", "line": 13, "column": 23, "nodeType": "302", "messageId": "310", "endLine": 13, "endColumn": 38}, {"ruleId": "300", "severity": 1, "message": "318", "line": 27, "column": 6, "nodeType": "312", "endLine": 27, "endColumn": 8, "suggestions": "319"}, {"ruleId": "320", "severity": 1, "message": "321", "line": 14, "column": 13, "nodeType": "322", "messageId": "323", "endLine": 14, "endColumn": 107, "fix": "324"}, {"ruleId": "300", "severity": 1, "message": "325", "line": 22, "column": 6, "nodeType": "312", "endLine": 22, "endColumn": 8, "suggestions": "326"}, {"ruleId": "304", "severity": 1, "message": "316", "line": 26, "column": 13, "nodeType": "306", "messageId": "307", "endLine": 26, "endColumn": 15}, {"ruleId": "308", "severity": 1, "message": "327", "line": 1, "column": 29, "nodeType": "302", "messageId": "310", "endLine": 1, "endColumn": 38}, {"ruleId": "304", "severity": 1, "message": "316", "line": 25, "column": 13, "nodeType": "306", "messageId": "307", "endLine": 25, "endColumn": 15}, {"ruleId": "300", "severity": 1, "message": "328", "line": 34, "column": 6, "nodeType": "312", "endLine": 34, "endColumn": 8, "suggestions": "329"}, {"ruleId": "304", "severity": 1, "message": "316", "line": 43, "column": 13, "nodeType": "306", "messageId": "307", "endLine": 43, "endColumn": 15}, {"ruleId": "300", "severity": 1, "message": "330", "line": 31, "column": 6, "nodeType": "312", "endLine": 31, "endColumn": 8, "suggestions": "331"}, {"ruleId": "332", "severity": 1, "message": "333", "line": 18, "column": 28, "nodeType": "302", "messageId": "334", "endLine": 18, "endColumn": 36}, {"ruleId": "300", "severity": 1, "message": "318", "line": 22, "column": 6, "nodeType": "312", "endLine": 22, "endColumn": 8, "suggestions": "335"}, {"ruleId": "300", "severity": 1, "message": "330", "line": 22, "column": 6, "nodeType": "312", "endLine": 22, "endColumn": 8, "suggestions": "336"}, {"ruleId": "300", "severity": 1, "message": "337", "line": 15, "column": 6, "nodeType": "312", "endLine": 15, "endColumn": 8, "suggestions": "338"}, {"ruleId": "308", "severity": 1, "message": "339", "line": 29, "column": 11, "nodeType": "302", "messageId": "310", "endLine": 29, "endColumn": 13}, {"ruleId": "304", "severity": 1, "message": "305", "line": 59, "column": 38, "nodeType": "306", "messageId": "307", "endLine": 59, "endColumn": 40}, {"ruleId": "304", "severity": 1, "message": "305", "line": 129, "column": 53, "nodeType": "306", "messageId": "307", "endLine": 129, "endColumn": 55}, {"ruleId": "304", "severity": 1, "message": "305", "line": 37, "column": 33, "nodeType": "306", "messageId": "307", "endLine": 37, "endColumn": 35}, {"ruleId": "304", "severity": 1, "message": "305", "line": 26, "column": 47, "nodeType": "306", "messageId": "307", "endLine": 26, "endColumn": 49}, {"ruleId": "304", "severity": 1, "message": "305", "line": 27, "column": 47, "nodeType": "306", "messageId": "307", "endLine": 27, "endColumn": 49}, {"ruleId": "300", "severity": 1, "message": "340", "line": 34, "column": 6, "nodeType": "312", "endLine": 34, "endColumn": 8, "suggestions": "341"}, {"ruleId": "304", "severity": 1, "message": "316", "line": 40, "column": 17, "nodeType": "306", "messageId": "307", "endLine": 40, "endColumn": 19}, {"ruleId": "308", "severity": 1, "message": "317", "line": 18, "column": 24, "nodeType": "302", "messageId": "310", "endLine": 18, "endColumn": 39}, {"ruleId": "300", "severity": 1, "message": "342", "line": 43, "column": 6, "nodeType": "312", "endLine": 43, "endColumn": 8, "suggestions": "343"}, {"ruleId": "304", "severity": 1, "message": "316", "line": 49, "column": 21, "nodeType": "306", "messageId": "307", "endLine": 49, "endColumn": 23}, {"ruleId": "304", "severity": 1, "message": "305", "line": 51, "column": 34, "nodeType": "306", "messageId": "307", "endLine": 51, "endColumn": 36}, {"ruleId": "304", "severity": 1, "message": "316", "line": 55, "column": 24, "nodeType": "306", "messageId": "307", "endLine": 55, "endColumn": 26}, {"ruleId": "304", "severity": 1, "message": "305", "line": 57, "column": 37, "nodeType": "306", "messageId": "307", "endLine": 57, "endColumn": 39}, {"ruleId": "304", "severity": 1, "message": "316", "line": 63, "column": 13, "nodeType": "306", "messageId": "307", "endLine": 63, "endColumn": 15}, {"ruleId": "304", "severity": 1, "message": "316", "line": 63, "column": 35, "nodeType": "306", "messageId": "307", "endLine": 63, "endColumn": 37}, {"ruleId": "304", "severity": 1, "message": "316", "line": 66, "column": 20, "nodeType": "306", "messageId": "307", "endLine": 66, "endColumn": 22}, {"ruleId": "304", "severity": 1, "message": "305", "line": 13, "column": 13, "nodeType": "306", "messageId": "307", "endLine": 13, "endColumn": 15}, {"ruleId": "344", "severity": 1, "message": "345", "line": 46, "column": 1, "nodeType": "346", "endLine": 53, "endColumn": 3}, {"ruleId": "308", "severity": 1, "message": "327", "line": 1, "column": 17, "nodeType": "302", "messageId": "310", "endLine": 1, "endColumn": 26}, {"ruleId": "304", "severity": 1, "message": "305", "line": 90, "column": 34, "nodeType": "306", "messageId": "307", "endLine": 90, "endColumn": 36}, {"ruleId": "304", "severity": 1, "message": "305", "line": 90, "column": 51, "nodeType": "306", "messageId": "307", "endLine": 90, "endColumn": 53}, {"ruleId": "304", "severity": 1, "message": "305", "line": 21, "column": 47, "nodeType": "306", "messageId": "307", "endLine": 21, "endColumn": 49}, {"ruleId": "308", "severity": 1, "message": "327", "line": 1, "column": 17, "nodeType": "302", "messageId": "310", "endLine": 1, "endColumn": 26}, {"ruleId": "308", "severity": 1, "message": "347", "line": 8, "column": 8, "nodeType": "302", "messageId": "310", "endLine": 8, "endColumn": 19}, {"ruleId": "308", "severity": 1, "message": "348", "line": 14, "column": 10, "nodeType": "302", "messageId": "310", "endLine": 14, "endColumn": 15}, {"ruleId": "308", "severity": 1, "message": "349", "line": 9, "column": 10, "nodeType": "302", "messageId": "310", "endLine": 9, "endColumn": 18}, {"ruleId": "308", "severity": 1, "message": "350", "line": 1, "column": 8, "nodeType": "302", "messageId": "310", "endLine": 1, "endColumn": 13}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["351"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["352"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["353"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["354"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "355", "text": "356"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["357"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["358"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["359"], "no-const-assign", "'redirect' is constant.", "const", ["360"], ["361"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["362"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["363"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["364"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'products' is assigned a value but never used.", "'React' is defined but never used.", {"desc": "365", "fix": "366"}, {"desc": "367", "fix": "368"}, {"desc": "367", "fix": "369"}, {"desc": "370", "fix": "371"}, [447, 447], " rel=\"noreferrer\"", {"desc": "372", "fix": "373"}, {"desc": "374", "fix": "375"}, {"desc": "376", "fix": "377"}, {"desc": "370", "fix": "378"}, {"desc": "376", "fix": "379"}, {"desc": "380", "fix": "381"}, {"desc": "382", "fix": "383"}, {"desc": "384", "fix": "385"}, "Add dependencies array: [keywordParam]", {"range": "386", "text": "387"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "388", "text": "389"}, {"range": "390", "text": "389"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "391", "text": "392"}, "Update the dependencies array to be: [loadProducts]", {"range": "393", "text": "394"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "395", "text": "396"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "397", "text": "398"}, {"range": "399", "text": "392"}, {"range": "400", "text": "398"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "401", "text": "402"}, "Update the dependencies array to be: [id, logout]", {"range": "403", "text": "404"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "405", "text": "406"}, [1834, 1834], ", [keywordParam]", [2796, 2798], "[authTokens, refresh]", [3172, 3184], [956, 958], "[navigate, redirect, userInfo]", [839, 841], "[loadProducts]", [1011, 1013], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1286, 1288], "[id, logout]", [1488, 1490], "[brandParam, categoryParam, loadProducts]"]