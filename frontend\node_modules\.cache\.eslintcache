[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "47", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "48"}, {"size": 649, "mtime": 1750943544495, "results": "49", "hashOfConfig": "50"}, {"size": 362, "mtime": 1750943544502, "results": "51", "hashOfConfig": "50"}, {"size": 5092, "mtime": 1750995236291, "results": "52", "hashOfConfig": "50"}, {"size": 1585, "mtime": 1750943544494, "results": "53", "hashOfConfig": "50"}, {"size": 4116, "mtime": 1750943544494, "results": "54", "hashOfConfig": "50"}, {"size": 4438, "mtime": 1750993170933, "results": "55", "hashOfConfig": "50"}, {"size": 1795, "mtime": 1750943544489, "results": "56", "hashOfConfig": "50"}, {"size": 3086, "mtime": 1750993154771, "results": "57", "hashOfConfig": "50"}, {"size": 562, "mtime": 1750943544488, "results": "58", "hashOfConfig": "50"}, {"size": 1986, "mtime": 1750996901833, "results": "59", "hashOfConfig": "50"}, {"size": 4041, "mtime": 1750943544496, "results": "60", "hashOfConfig": "50"}, {"size": 4556, "mtime": 1750943544500, "results": "61", "hashOfConfig": "50"}, {"size": 3166, "mtime": 1750943544501, "results": "62", "hashOfConfig": "50"}, {"size": 3374, "mtime": 1750943544501, "results": "63", "hashOfConfig": "50"}, {"size": 2746, "mtime": 1750943544500, "results": "64", "hashOfConfig": "50"}, {"size": 557, "mtime": 1750943544498, "results": "65", "hashOfConfig": "50"}, {"size": 1873, "mtime": 1750945277042, "results": "66", "hashOfConfig": "50"}, {"size": 5003, "mtime": 1750943544499, "results": "67", "hashOfConfig": "50"}, {"size": 1876, "mtime": 1750943544499, "results": "68", "hashOfConfig": "50"}, {"size": 6146, "mtime": 1750943544499, "results": "69", "hashOfConfig": "50"}, {"size": 4610, "mtime": 1750997032701, "results": "70", "hashOfConfig": "50"}, {"size": 392, "mtime": 1750943544489, "results": "71", "hashOfConfig": "50"}, {"size": 988, "mtime": 1750943544487, "results": "72", "hashOfConfig": "50"}, {"size": 373, "mtime": 1751032743405, "results": "73", "hashOfConfig": "50"}, {"size": 1221, "mtime": 1750943544493, "results": "74", "hashOfConfig": "50"}, {"size": 228, "mtime": 1750943544490, "results": "75", "hashOfConfig": "50"}, {"size": 1101, "mtime": 1750943544491, "results": "76", "hashOfConfig": "50"}, {"size": 737, "mtime": 1750943544487, "results": "77", "hashOfConfig": "50"}, {"size": 901, "mtime": 1750943544491, "results": "78", "hashOfConfig": "50"}, {"size": 3665, "mtime": 1750943544493, "results": "79", "hashOfConfig": "50"}, {"size": 372, "mtime": 1750943544489, "results": "80", "hashOfConfig": "50"}, {"size": 2548, "mtime": 1750943544493, "results": "81", "hashOfConfig": "50"}, {"size": 2457, "mtime": 1750996243341, "results": "82", "hashOfConfig": "50"}, {"size": 1519, "mtime": 1750943544488, "results": "83", "hashOfConfig": "50"}, {"size": 639, "mtime": 1750943544492, "results": "84", "hashOfConfig": "50"}, {"size": 1826, "mtime": 1750943544491, "results": "85", "hashOfConfig": "50"}, {"size": 11285, "mtime": 1750993002543, "results": "86", "hashOfConfig": "50"}, {"size": 10483, "mtime": 1750993066396, "results": "87", "hashOfConfig": "50"}, {"size": 7625, "mtime": 1751031499765, "results": "88", "hashOfConfig": "50"}, {"size": 445, "mtime": 1750994572623, "results": "89", "hashOfConfig": "50"}, {"size": 3134, "mtime": 1750997224891, "results": "90", "hashOfConfig": "50"}, {"size": 3307, "mtime": 1750993684234, "results": "91", "hashOfConfig": "50"}, {"size": 8263, "mtime": 1751031465839, "results": "92", "hashOfConfig": "50"}, {"size": 551, "mtime": 1750993273555, "results": "93", "hashOfConfig": "50"}, {"size": 8081, "mtime": 1751031022948, "results": "94", "hashOfConfig": "50"}, {"size": 14883, "mtime": 1751033321582, "results": "95", "hashOfConfig": "50"}, {"size": 8445, "mtime": 1751031717046, "results": "96", "hashOfConfig": "50"}, {"size": 702, "mtime": 1750996844606, "results": "97", "hashOfConfig": "50"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["242"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["243"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["244", "245", "246"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["247", "248", "249", "250", "251", "252", "253", "254", "255"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["256", "257"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["258"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["259", "260"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["261", "262"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["263", "264"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["265"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["266", "267"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["268"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["269"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["270", "271", "272"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["273"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["274", "275", "276", "277"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["278", "279", "280", "281", "282", "283", "284", "285", "286"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["287", "288"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["289"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["290", "291"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["292"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["293", "294", "295"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["296"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["297"], [], {"ruleId": "298", "severity": 1, "message": "299", "line": 41, "column": 3, "nodeType": "300", "endLine": 41, "endColumn": 12, "suggestions": "301"}, {"ruleId": "302", "severity": 1, "message": "303", "line": 35, "column": 49, "nodeType": "304", "messageId": "305", "endLine": 35, "endColumn": 51}, {"ruleId": "302", "severity": 1, "message": "303", "line": 62, "column": 18, "nodeType": "304", "messageId": "305", "endLine": 62, "endColumn": 20}, {"ruleId": "302", "severity": 1, "message": "303", "line": 68, "column": 15, "nodeType": "304", "messageId": "305", "endLine": 68, "endColumn": 17}, {"ruleId": "302", "severity": 1, "message": "303", "line": 125, "column": 45, "nodeType": "304", "messageId": "305", "endLine": 125, "endColumn": 47}, {"ruleId": "306", "severity": 1, "message": "307", "line": 58, "column": 15, "nodeType": "300", "messageId": "308", "endLine": 58, "endColumn": 19}, {"ruleId": "298", "severity": 1, "message": "309", "line": 104, "column": 6, "nodeType": "310", "endLine": 104, "endColumn": 8, "suggestions": "311"}, {"ruleId": "298", "severity": 1, "message": "312", "line": 116, "column": 6, "nodeType": "310", "endLine": 116, "endColumn": 18, "suggestions": "313"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 121, "column": 20, "nodeType": "304", "messageId": "305", "endLine": 121, "endColumn": 22}, {"ruleId": "302", "severity": 1, "message": "314", "line": 121, "column": 53, "nodeType": "304", "messageId": "305", "endLine": 121, "endColumn": 55}, {"ruleId": "302", "severity": 1, "message": "314", "line": 125, "column": 17, "nodeType": "304", "messageId": "305", "endLine": 125, "endColumn": 19}, {"ruleId": "302", "severity": 1, "message": "314", "line": 125, "column": 44, "nodeType": "304", "messageId": "305", "endLine": 125, "endColumn": 46}, {"ruleId": "302", "severity": 1, "message": "314", "line": 129, "column": 20, "nodeType": "304", "messageId": "305", "endLine": 129, "endColumn": 22}, {"ruleId": "302", "severity": 1, "message": "303", "line": 133, "column": 16, "nodeType": "304", "messageId": "305", "endLine": 133, "endColumn": 18}, {"ruleId": "306", "severity": 1, "message": "315", "line": 13, "column": 23, "nodeType": "300", "messageId": "308", "endLine": 13, "endColumn": 38}, {"ruleId": "298", "severity": 1, "message": "316", "line": 27, "column": 6, "nodeType": "310", "endLine": 27, "endColumn": 8, "suggestions": "317"}, {"ruleId": "318", "severity": 1, "message": "319", "line": 14, "column": 13, "nodeType": "320", "messageId": "321", "endLine": 14, "endColumn": 107, "fix": "322"}, {"ruleId": "298", "severity": 1, "message": "323", "line": 22, "column": 6, "nodeType": "310", "endLine": 22, "endColumn": 8, "suggestions": "324"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 26, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 26, "endColumn": 15}, {"ruleId": "306", "severity": 1, "message": "325", "line": 1, "column": 29, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 38}, {"ruleId": "302", "severity": 1, "message": "314", "line": 25, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 25, "endColumn": 15}, {"ruleId": "298", "severity": 1, "message": "326", "line": 34, "column": 6, "nodeType": "310", "endLine": 34, "endColumn": 8, "suggestions": "327"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 43, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 43, "endColumn": 15}, {"ruleId": "298", "severity": 1, "message": "328", "line": 31, "column": 6, "nodeType": "310", "endLine": 31, "endColumn": 8, "suggestions": "329"}, {"ruleId": "330", "severity": 1, "message": "331", "line": 18, "column": 28, "nodeType": "300", "messageId": "332", "endLine": 18, "endColumn": 36}, {"ruleId": "298", "severity": 1, "message": "316", "line": 22, "column": 6, "nodeType": "310", "endLine": 22, "endColumn": 8, "suggestions": "333"}, {"ruleId": "298", "severity": 1, "message": "328", "line": 22, "column": 6, "nodeType": "310", "endLine": 22, "endColumn": 8, "suggestions": "334"}, {"ruleId": "298", "severity": 1, "message": "335", "line": 15, "column": 6, "nodeType": "310", "endLine": 15, "endColumn": 8, "suggestions": "336"}, {"ruleId": "306", "severity": 1, "message": "337", "line": 29, "column": 11, "nodeType": "300", "messageId": "308", "endLine": 29, "endColumn": 13}, {"ruleId": "302", "severity": 1, "message": "303", "line": 59, "column": 38, "nodeType": "304", "messageId": "305", "endLine": 59, "endColumn": 40}, {"ruleId": "302", "severity": 1, "message": "303", "line": 129, "column": 53, "nodeType": "304", "messageId": "305", "endLine": 129, "endColumn": 55}, {"ruleId": "302", "severity": 1, "message": "303", "line": 37, "column": 33, "nodeType": "304", "messageId": "305", "endLine": 37, "endColumn": 35}, {"ruleId": "302", "severity": 1, "message": "303", "line": 26, "column": 47, "nodeType": "304", "messageId": "305", "endLine": 26, "endColumn": 49}, {"ruleId": "302", "severity": 1, "message": "303", "line": 27, "column": 47, "nodeType": "304", "messageId": "305", "endLine": 27, "endColumn": 49}, {"ruleId": "298", "severity": 1, "message": "338", "line": 34, "column": 6, "nodeType": "310", "endLine": 34, "endColumn": 8, "suggestions": "339"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 40, "column": 17, "nodeType": "304", "messageId": "305", "endLine": 40, "endColumn": 19}, {"ruleId": "306", "severity": 1, "message": "315", "line": 18, "column": 24, "nodeType": "300", "messageId": "308", "endLine": 18, "endColumn": 39}, {"ruleId": "298", "severity": 1, "message": "340", "line": 43, "column": 6, "nodeType": "310", "endLine": 43, "endColumn": 8, "suggestions": "341"}, {"ruleId": "302", "severity": 1, "message": "314", "line": 49, "column": 21, "nodeType": "304", "messageId": "305", "endLine": 49, "endColumn": 23}, {"ruleId": "302", "severity": 1, "message": "303", "line": 51, "column": 34, "nodeType": "304", "messageId": "305", "endLine": 51, "endColumn": 36}, {"ruleId": "302", "severity": 1, "message": "314", "line": 55, "column": 24, "nodeType": "304", "messageId": "305", "endLine": 55, "endColumn": 26}, {"ruleId": "302", "severity": 1, "message": "303", "line": 57, "column": 37, "nodeType": "304", "messageId": "305", "endLine": 57, "endColumn": 39}, {"ruleId": "302", "severity": 1, "message": "314", "line": 63, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 63, "endColumn": 15}, {"ruleId": "302", "severity": 1, "message": "314", "line": 63, "column": 35, "nodeType": "304", "messageId": "305", "endLine": 63, "endColumn": 37}, {"ruleId": "302", "severity": 1, "message": "314", "line": 66, "column": 20, "nodeType": "304", "messageId": "305", "endLine": 66, "endColumn": 22}, {"ruleId": "302", "severity": 1, "message": "303", "line": 4, "column": 13, "nodeType": "304", "messageId": "305", "endLine": 4, "endColumn": 15}, {"ruleId": "342", "severity": 1, "message": "343", "line": 11, "column": 1, "nodeType": "344", "endLine": 18, "endColumn": 3}, {"ruleId": "306", "severity": 1, "message": "325", "line": 1, "column": 17, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 26}, {"ruleId": "302", "severity": 1, "message": "303", "line": 90, "column": 34, "nodeType": "304", "messageId": "305", "endLine": 90, "endColumn": 36}, {"ruleId": "302", "severity": 1, "message": "303", "line": 90, "column": 51, "nodeType": "304", "messageId": "305", "endLine": 90, "endColumn": 53}, {"ruleId": "302", "severity": 1, "message": "303", "line": 21, "column": 47, "nodeType": "304", "messageId": "305", "endLine": 21, "endColumn": 49}, {"ruleId": "306", "severity": 1, "message": "325", "line": 1, "column": 17, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 26}, {"ruleId": "306", "severity": 1, "message": "345", "line": 8, "column": 8, "nodeType": "300", "messageId": "308", "endLine": 8, "endColumn": 19}, {"ruleId": "306", "severity": 1, "message": "346", "line": 14, "column": 10, "nodeType": "300", "messageId": "308", "endLine": 14, "endColumn": 15}, {"ruleId": "306", "severity": 1, "message": "347", "line": 9, "column": 10, "nodeType": "300", "messageId": "308", "endLine": 9, "endColumn": 18}, {"ruleId": "306", "severity": 1, "message": "348", "line": 1, "column": 8, "nodeType": "300", "messageId": "308", "endLine": 1, "endColumn": 13}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["349"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["350"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["351"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["352"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "353", "text": "354"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["355"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["356"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["357"], "no-const-assign", "'redirect' is constant.", "const", ["358"], ["359"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["360"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["361"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["362"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'products' is assigned a value but never used.", "'React' is defined but never used.", {"desc": "363", "fix": "364"}, {"desc": "365", "fix": "366"}, {"desc": "365", "fix": "367"}, {"desc": "368", "fix": "369"}, [447, 447], " rel=\"noreferrer\"", {"desc": "370", "fix": "371"}, {"desc": "372", "fix": "373"}, {"desc": "374", "fix": "375"}, {"desc": "368", "fix": "376"}, {"desc": "374", "fix": "377"}, {"desc": "378", "fix": "379"}, {"desc": "380", "fix": "381"}, {"desc": "382", "fix": "383"}, "Add dependencies array: [keywordParam]", {"range": "384", "text": "385"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "386", "text": "387"}, {"range": "388", "text": "387"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "389", "text": "390"}, "Update the dependencies array to be: [loadProducts]", {"range": "391", "text": "392"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "393", "text": "394"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "395", "text": "396"}, {"range": "397", "text": "390"}, {"range": "398", "text": "396"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "399", "text": "400"}, "Update the dependencies array to be: [id, logout]", {"range": "401", "text": "402"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "403", "text": "404"}, [1834, 1834], ", [keywordParam]", [2796, 2798], "[authTokens, refresh]", [3172, 3184], [956, 958], "[navigate, redirect, userInfo]", [839, 841], "[loadProducts]", [1011, 1013], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1286, 1288], "[id, logout]", [1488, 1490], "[brandParam, categoryParam, loadProducts]"]