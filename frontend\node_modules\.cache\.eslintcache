[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "47", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "48", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\utils\\currency.js": "49"}, {"size": 649, "mtime": 1750943544495, "results": "50", "hashOfConfig": "51"}, {"size": 362, "mtime": 1750943544502, "results": "52", "hashOfConfig": "51"}, {"size": 5092, "mtime": 1750995236291, "results": "53", "hashOfConfig": "51"}, {"size": 1585, "mtime": 1750943544494, "results": "54", "hashOfConfig": "51"}, {"size": 4295, "mtime": 1751101003380, "results": "55", "hashOfConfig": "51"}, {"size": 4438, "mtime": 1750993170933, "results": "56", "hashOfConfig": "51"}, {"size": 1795, "mtime": 1750943544489, "results": "57", "hashOfConfig": "51"}, {"size": 3086, "mtime": 1750993154771, "results": "58", "hashOfConfig": "51"}, {"size": 562, "mtime": 1750943544488, "results": "59", "hashOfConfig": "51"}, {"size": 1986, "mtime": 1750996901833, "results": "60", "hashOfConfig": "51"}, {"size": 4038, "mtime": 1751100844090, "results": "61", "hashOfConfig": "51"}, {"size": 4581, "mtime": 1751100922373, "results": "62", "hashOfConfig": "51"}, {"size": 3166, "mtime": 1750943544501, "results": "63", "hashOfConfig": "51"}, {"size": 3374, "mtime": 1750943544501, "results": "64", "hashOfConfig": "51"}, {"size": 2746, "mtime": 1750943544500, "results": "65", "hashOfConfig": "51"}, {"size": 557, "mtime": 1750943544498, "results": "66", "hashOfConfig": "51"}, {"size": 1873, "mtime": 1750945277042, "results": "67", "hashOfConfig": "51"}, {"size": 5126, "mtime": 1751101043525, "results": "68", "hashOfConfig": "51"}, {"size": 1876, "mtime": 1750943544499, "results": "69", "hashOfConfig": "51"}, {"size": 6225, "mtime": 1751100861367, "results": "70", "hashOfConfig": "51"}, {"size": 4610, "mtime": 1750997032701, "results": "71", "hashOfConfig": "51"}, {"size": 392, "mtime": 1750943544489, "results": "72", "hashOfConfig": "51"}, {"size": 988, "mtime": 1750943544487, "results": "73", "hashOfConfig": "51"}, {"size": 1572, "mtime": 1751033967838, "results": "74", "hashOfConfig": "51"}, {"size": 1221, "mtime": 1750943544493, "results": "75", "hashOfConfig": "51"}, {"size": 228, "mtime": 1750943544490, "results": "76", "hashOfConfig": "51"}, {"size": 1126, "mtime": 1751100830263, "results": "77", "hashOfConfig": "51"}, {"size": 737, "mtime": 1750943544487, "results": "78", "hashOfConfig": "51"}, {"size": 901, "mtime": 1750943544491, "results": "79", "hashOfConfig": "51"}, {"size": 3665, "mtime": 1750943544493, "results": "80", "hashOfConfig": "51"}, {"size": 372, "mtime": 1750943544489, "results": "81", "hashOfConfig": "51"}, {"size": 2548, "mtime": 1750943544493, "results": "82", "hashOfConfig": "51"}, {"size": 2457, "mtime": 1750996243341, "results": "83", "hashOfConfig": "51"}, {"size": 1519, "mtime": 1750943544488, "results": "84", "hashOfConfig": "51"}, {"size": 639, "mtime": 1750943544492, "results": "85", "hashOfConfig": "51"}, {"size": 1826, "mtime": 1750943544491, "results": "86", "hashOfConfig": "51"}, {"size": 11312, "mtime": 1751100901781, "results": "87", "hashOfConfig": "51"}, {"size": 10510, "mtime": 1751100888901, "results": "88", "hashOfConfig": "51"}, {"size": 7625, "mtime": 1751031499765, "results": "89", "hashOfConfig": "51"}, {"size": 445, "mtime": 1750994572623, "results": "90", "hashOfConfig": "51"}, {"size": 3134, "mtime": 1750997224891, "results": "91", "hashOfConfig": "51"}, {"size": 3307, "mtime": 1750993684234, "results": "92", "hashOfConfig": "51"}, {"size": 8263, "mtime": 1751031465839, "results": "93", "hashOfConfig": "51"}, {"size": 551, "mtime": 1750993273555, "results": "94", "hashOfConfig": "51"}, {"size": 8081, "mtime": 1751031022948, "results": "95", "hashOfConfig": "51"}, {"size": 13462, "mtime": 1751035160109, "results": "96", "hashOfConfig": "51"}, {"size": 8445, "mtime": 1751031717046, "results": "97", "hashOfConfig": "51"}, {"size": 702, "mtime": 1750996844606, "results": "98", "hashOfConfig": "51"}, {"size": 1091, "mtime": 1751100972472, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "172", "usedDeprecatedRules": "173"}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["249"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["250"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["251", "252", "253"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["254", "255", "256", "257", "258", "259", "260", "261", "262"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["263", "264"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["265"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["266", "267"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["268", "269"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["270", "271"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["272"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["273", "274"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["275"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["276"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["277", "278", "279"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["280"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["281", "282", "283", "284"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["285", "286", "287", "288", "289", "290", "291", "292", "293"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["294", "295"], [], "import axios from \"axios\";\n\n// Set base URL directly to Django backend\naxios.defaults.baseURL = 'http://localhost:8000';\n\n// Set default headers for JSON\naxios.defaults.headers.common['Content-Type'] = 'application/json';\naxios.defaults.headers.put['Content-Type'] = 'application/json';\naxios.defaults.headers.post['Content-Type'] = 'application/json';\naxios.defaults.headers.patch['Content-Type'] = 'application/json';\n\nfunction setJwt(jwt) {\n    if (jwt == undefined) {\n        delete axios.defaults.headers.common[\"Authorization\"];\n        return;\n    }\n    axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n    console.log('JWT token set:', `JWT ${jwt.substring(0, 20)}...`);\n}\n\n// Add request interceptor to log requests\naxios.interceptors.request.use(\n    (config) => {\n        console.log('Making request:', config.method?.toUpperCase(), config.url);\n        console.log('Headers:', config.headers);\n        return config;\n    },\n    (error) => {\n        return Promise.reject(error);\n    }\n);\n\n// Add response interceptor to log responses\naxios.interceptors.response.use(\n    (response) => {\n        console.log('Response received:', response.status, response.config.url);\n        return response;\n    },\n    (error) => {\n        console.log('Request failed:', error.response?.status, error.config?.url);\n        console.log('Error details:', error.response?.data);\n        return Promise.reject(error);\n    }\n);\n\nexport default {\n    get:axios.get,\n    post:axios.post,\n    put:axios.put,\n    patch:axios.patch,\n    delete:axios.delete,\n    setJwt\n};", [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["296"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["297", "298"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["299"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["300", "301", "302"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["303"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["304"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\utils\\currency.js", [], [], {"ruleId": "305", "severity": 1, "message": "306", "line": 41, "column": 3, "nodeType": "307", "endLine": 41, "endColumn": 12, "suggestions": "308"}, {"ruleId": "309", "severity": 1, "message": "310", "line": 35, "column": 49, "nodeType": "311", "messageId": "312", "endLine": 35, "endColumn": 51}, {"ruleId": "309", "severity": 1, "message": "310", "line": 63, "column": 18, "nodeType": "311", "messageId": "312", "endLine": 63, "endColumn": 20}, {"ruleId": "309", "severity": 1, "message": "310", "line": 69, "column": 15, "nodeType": "311", "messageId": "312", "endLine": 69, "endColumn": 17}, {"ruleId": "309", "severity": 1, "message": "310", "line": 128, "column": 45, "nodeType": "311", "messageId": "312", "endLine": 128, "endColumn": 47}, {"ruleId": "313", "severity": 1, "message": "314", "line": 58, "column": 15, "nodeType": "307", "messageId": "315", "endLine": 58, "endColumn": 19}, {"ruleId": "305", "severity": 1, "message": "316", "line": 104, "column": 6, "nodeType": "317", "endLine": 104, "endColumn": 8, "suggestions": "318"}, {"ruleId": "305", "severity": 1, "message": "319", "line": 116, "column": 6, "nodeType": "317", "endLine": 116, "endColumn": 18, "suggestions": "320"}, {"ruleId": "309", "severity": 1, "message": "321", "line": 121, "column": 20, "nodeType": "311", "messageId": "312", "endLine": 121, "endColumn": 22}, {"ruleId": "309", "severity": 1, "message": "321", "line": 121, "column": 53, "nodeType": "311", "messageId": "312", "endLine": 121, "endColumn": 55}, {"ruleId": "309", "severity": 1, "message": "321", "line": 125, "column": 17, "nodeType": "311", "messageId": "312", "endLine": 125, "endColumn": 19}, {"ruleId": "309", "severity": 1, "message": "321", "line": 125, "column": 44, "nodeType": "311", "messageId": "312", "endLine": 125, "endColumn": 46}, {"ruleId": "309", "severity": 1, "message": "321", "line": 129, "column": 20, "nodeType": "311", "messageId": "312", "endLine": 129, "endColumn": 22}, {"ruleId": "309", "severity": 1, "message": "310", "line": 133, "column": 16, "nodeType": "311", "messageId": "312", "endLine": 133, "endColumn": 18}, {"ruleId": "313", "severity": 1, "message": "322", "line": 13, "column": 23, "nodeType": "307", "messageId": "315", "endLine": 13, "endColumn": 38}, {"ruleId": "305", "severity": 1, "message": "323", "line": 27, "column": 6, "nodeType": "317", "endLine": 27, "endColumn": 8, "suggestions": "324"}, {"ruleId": "325", "severity": 1, "message": "326", "line": 14, "column": 13, "nodeType": "327", "messageId": "328", "endLine": 14, "endColumn": 107, "fix": "329"}, {"ruleId": "305", "severity": 1, "message": "330", "line": 22, "column": 6, "nodeType": "317", "endLine": 22, "endColumn": 8, "suggestions": "331"}, {"ruleId": "309", "severity": 1, "message": "321", "line": 26, "column": 13, "nodeType": "311", "messageId": "312", "endLine": 26, "endColumn": 15}, {"ruleId": "313", "severity": 1, "message": "332", "line": 1, "column": 29, "nodeType": "307", "messageId": "315", "endLine": 1, "endColumn": 38}, {"ruleId": "309", "severity": 1, "message": "321", "line": 25, "column": 13, "nodeType": "311", "messageId": "312", "endLine": 25, "endColumn": 15}, {"ruleId": "305", "severity": 1, "message": "333", "line": 34, "column": 6, "nodeType": "317", "endLine": 34, "endColumn": 8, "suggestions": "334"}, {"ruleId": "309", "severity": 1, "message": "321", "line": 43, "column": 13, "nodeType": "311", "messageId": "312", "endLine": 43, "endColumn": 15}, {"ruleId": "305", "severity": 1, "message": "335", "line": 31, "column": 6, "nodeType": "317", "endLine": 31, "endColumn": 8, "suggestions": "336"}, {"ruleId": "337", "severity": 1, "message": "338", "line": 18, "column": 28, "nodeType": "307", "messageId": "339", "endLine": 18, "endColumn": 36}, {"ruleId": "305", "severity": 1, "message": "323", "line": 22, "column": 6, "nodeType": "317", "endLine": 22, "endColumn": 8, "suggestions": "340"}, {"ruleId": "305", "severity": 1, "message": "335", "line": 22, "column": 6, "nodeType": "317", "endLine": 22, "endColumn": 8, "suggestions": "341"}, {"ruleId": "305", "severity": 1, "message": "342", "line": 15, "column": 6, "nodeType": "317", "endLine": 15, "endColumn": 8, "suggestions": "343"}, {"ruleId": "313", "severity": 1, "message": "344", "line": 30, "column": 11, "nodeType": "307", "messageId": "315", "endLine": 30, "endColumn": 13}, {"ruleId": "309", "severity": 1, "message": "310", "line": 60, "column": 38, "nodeType": "311", "messageId": "312", "endLine": 60, "endColumn": 40}, {"ruleId": "309", "severity": 1, "message": "310", "line": 130, "column": 53, "nodeType": "311", "messageId": "312", "endLine": 130, "endColumn": 55}, {"ruleId": "309", "severity": 1, "message": "310", "line": 37, "column": 33, "nodeType": "311", "messageId": "312", "endLine": 37, "endColumn": 35}, {"ruleId": "309", "severity": 1, "message": "310", "line": 26, "column": 47, "nodeType": "311", "messageId": "312", "endLine": 26, "endColumn": 49}, {"ruleId": "309", "severity": 1, "message": "310", "line": 27, "column": 47, "nodeType": "311", "messageId": "312", "endLine": 27, "endColumn": 49}, {"ruleId": "305", "severity": 1, "message": "345", "line": 34, "column": 6, "nodeType": "317", "endLine": 34, "endColumn": 8, "suggestions": "346"}, {"ruleId": "309", "severity": 1, "message": "321", "line": 40, "column": 17, "nodeType": "311", "messageId": "312", "endLine": 40, "endColumn": 19}, {"ruleId": "313", "severity": 1, "message": "322", "line": 18, "column": 24, "nodeType": "307", "messageId": "315", "endLine": 18, "endColumn": 39}, {"ruleId": "305", "severity": 1, "message": "347", "line": 43, "column": 6, "nodeType": "317", "endLine": 43, "endColumn": 8, "suggestions": "348"}, {"ruleId": "309", "severity": 1, "message": "321", "line": 49, "column": 21, "nodeType": "311", "messageId": "312", "endLine": 49, "endColumn": 23}, {"ruleId": "309", "severity": 1, "message": "310", "line": 51, "column": 34, "nodeType": "311", "messageId": "312", "endLine": 51, "endColumn": 36}, {"ruleId": "309", "severity": 1, "message": "321", "line": 55, "column": 24, "nodeType": "311", "messageId": "312", "endLine": 55, "endColumn": 26}, {"ruleId": "309", "severity": 1, "message": "310", "line": 57, "column": 37, "nodeType": "311", "messageId": "312", "endLine": 57, "endColumn": 39}, {"ruleId": "309", "severity": 1, "message": "321", "line": 63, "column": 13, "nodeType": "311", "messageId": "312", "endLine": 63, "endColumn": 15}, {"ruleId": "309", "severity": 1, "message": "321", "line": 63, "column": 35, "nodeType": "311", "messageId": "312", "endLine": 63, "endColumn": 37}, {"ruleId": "309", "severity": 1, "message": "321", "line": 66, "column": 20, "nodeType": "311", "messageId": "312", "endLine": 66, "endColumn": 22}, {"ruleId": "309", "severity": 1, "message": "310", "line": 13, "column": 13, "nodeType": "311", "messageId": "312", "endLine": 13, "endColumn": 15}, {"ruleId": "349", "severity": 1, "message": "350", "line": 46, "column": 1, "nodeType": "351", "endLine": 53, "endColumn": 3}, {"ruleId": "313", "severity": 1, "message": "332", "line": 1, "column": 17, "nodeType": "307", "messageId": "315", "endLine": 1, "endColumn": 26}, {"ruleId": "309", "severity": 1, "message": "310", "line": 90, "column": 34, "nodeType": "311", "messageId": "312", "endLine": 90, "endColumn": 36}, {"ruleId": "309", "severity": 1, "message": "310", "line": 90, "column": 51, "nodeType": "311", "messageId": "312", "endLine": 90, "endColumn": 53}, {"ruleId": "309", "severity": 1, "message": "310", "line": 21, "column": 47, "nodeType": "311", "messageId": "312", "endLine": 21, "endColumn": 49}, {"ruleId": "313", "severity": 1, "message": "332", "line": 1, "column": 17, "nodeType": "307", "messageId": "315", "endLine": 1, "endColumn": 26}, {"ruleId": "313", "severity": 1, "message": "352", "line": 8, "column": 8, "nodeType": "307", "messageId": "315", "endLine": 8, "endColumn": 19}, {"ruleId": "313", "severity": 1, "message": "353", "line": 14, "column": 10, "nodeType": "307", "messageId": "315", "endLine": 14, "endColumn": 15}, {"ruleId": "313", "severity": 1, "message": "354", "line": 9, "column": 10, "nodeType": "307", "messageId": "315", "endLine": 9, "endColumn": 18}, {"ruleId": "313", "severity": 1, "message": "355", "line": 1, "column": 8, "nodeType": "307", "messageId": "315", "endLine": 1, "endColumn": 13}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["356"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["357"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["358"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["359"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "360", "text": "361"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["362"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["363"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["364"], "no-const-assign", "'redirect' is constant.", "const", ["365"], ["366"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["367"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["368"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["369"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'products' is assigned a value but never used.", "'React' is defined but never used.", {"desc": "370", "fix": "371"}, {"desc": "372", "fix": "373"}, {"desc": "372", "fix": "374"}, {"desc": "375", "fix": "376"}, [447, 447], " rel=\"noreferrer\"", {"desc": "377", "fix": "378"}, {"desc": "379", "fix": "380"}, {"desc": "381", "fix": "382"}, {"desc": "375", "fix": "383"}, {"desc": "381", "fix": "384"}, {"desc": "385", "fix": "386"}, {"desc": "387", "fix": "388"}, {"desc": "389", "fix": "390"}, "Add dependencies array: [keywordParam]", {"range": "391", "text": "392"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "393", "text": "394"}, {"range": "395", "text": "394"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [loadProducts]", {"range": "398", "text": "399"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "400", "text": "401"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "402", "text": "403"}, {"range": "404", "text": "397"}, {"range": "405", "text": "403"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "406", "text": "407"}, "Update the dependencies array to be: [id, logout]", {"range": "408", "text": "409"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "410", "text": "411"}, [1834, 1834], ", [keywordParam]", [2796, 2798], "[authTokens, refresh]", [3172, 3184], [956, 958], "[navigate, redirect, userInfo]", [839, 841], "[loadProducts]", [1011, 1013], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1286, 1288], "[id, logout]", [1488, 1490], "[brandParam, categoryParam, loadProducts]"]