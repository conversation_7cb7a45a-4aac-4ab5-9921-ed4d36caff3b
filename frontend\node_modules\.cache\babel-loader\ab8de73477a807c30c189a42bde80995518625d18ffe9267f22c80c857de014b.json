{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\product.jsx\";\nimport React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport Rating from \"./rating\";\nimport { Link } from \"react-router-dom\";\nimport { formatVND } from \"../utils/currency\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Product(_ref) {\n  let {\n    product\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"my-3 p-3 rounded\",\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: `/products/${product.id}`,\n      onClick: () => {\n        window.scrollTo(0, 0);\n      },\n      children: /*#__PURE__*/_jsxDEV(Card.Img, {\n        src: product.image\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: `/products/${product.id}`,\n        className: \"text-decoration-none\",\n        onClick: () => {\n          window.scrollTo(0, 0);\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Title, {\n          as: \"div\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n        as: \"div\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-3\",\n          children: /*#__PURE__*/_jsxDEV(Rating, {\n            value: product.rating,\n            text: `${product.numReviews} reviews`,\n            color: \"#f8e825\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n        as: \"h3\",\n        children: formatVND(product.price)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = Product;\nexport default Product;\nvar _c;\n$RefreshReg$(_c, \"Product\");", "map": {"version": 3, "names": ["React", "Card", "Rating", "Link", "formatVND", "jsxDEV", "_jsxDEV", "Product", "_ref", "product", "className", "children", "to", "id", "onClick", "window", "scrollTo", "Img", "src", "image", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "Title", "as", "name", "Text", "value", "rating", "text", "numReviews", "color", "price", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/product.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport Rating from \"./rating\";\nimport { Link } from \"react-router-dom\";\nimport { formatVND } from \"../utils/currency\";\n\nfunction Product({ product }) {\n  return (\n    <Card className=\"my-3 p-3 rounded\">\n      <Link\n        to={`/products/${product.id}`}\n        onClick={() => {\n          window.scrollTo(0, 0);\n        }}\n      >\n        <Card.Img src={product.image} />\n      </Link>\n      <Card.Body>\n        <Link\n          to={`/products/${product.id}`}\n          className=\"text-decoration-none\"\n          onClick={() => {\n            window.scrollTo(0, 0);\n          }}\n        >\n          <Card.Title as=\"div\">\n            <strong>{product.name}</strong>\n          </Card.Title>\n        </Link>\n        <Card.Text as=\"div\">\n          <div className=\"my-3\">\n            <Rating\n              value={product.rating}\n              text={`${product.numReviews} reviews`}\n              color={\"#f8e825\"}\n            />\n          </div>\n        </Card.Text>\n        <Card.Text as=\"h3\">{formatVND(product.price)}</Card.Text>\n      </Card.Body>\n    </Card>\n  );\n}\n\nexport default Product;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,OAAOA,CAAAC,IAAA,EAAc;EAAA,IAAb;IAAEC;EAAQ,CAAC,GAAAD,IAAA;EAC1B,oBACEF,OAAA,CAACL,IAAI;IAACS,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAChCL,OAAA,CAACH,IAAI;MACHS,EAAE,EAAG,aAAYH,OAAO,CAACI,EAAG,EAAE;MAC9BC,OAAO,EAAEA,CAAA,KAAM;QACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAE;MAAAL,QAAA,eAEFL,OAAA,CAACL,IAAI,CAACgB,GAAG;QAACC,GAAG,EAAET,OAAO,CAACU;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC3B,eACPjB,OAAA,CAACL,IAAI,CAACuB,IAAI;MAAAb,QAAA,gBACRL,OAAA,CAACH,IAAI;QACHS,EAAE,EAAG,aAAYH,OAAO,CAACI,EAAG,EAAE;QAC9BH,SAAS,EAAC,sBAAsB;QAChCI,OAAO,EAAEA,CAAA,KAAM;UACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACvB,CAAE;QAAAL,QAAA,eAEFL,OAAA,CAACL,IAAI,CAACwB,KAAK;UAACC,EAAE,EAAC,KAAK;UAAAf,QAAA,eAClBL,OAAA;YAAAK,QAAA,EAASF,OAAO,CAACkB;UAAI;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACpB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACR,eACPjB,OAAA,CAACL,IAAI,CAAC2B,IAAI;QAACF,EAAE,EAAC,KAAK;QAAAf,QAAA,eACjBL,OAAA;UAAKI,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBL,OAAA,CAACJ,MAAM;YACL2B,KAAK,EAAEpB,OAAO,CAACqB,MAAO;YACtBC,IAAI,EAAG,GAAEtB,OAAO,CAACuB,UAAW,UAAU;YACtCC,KAAK,EAAE;UAAU;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACI,eACZjB,OAAA,CAACL,IAAI,CAAC2B,IAAI;QAACF,EAAE,EAAC,IAAI;QAAAf,QAAA,EAAEP,SAAS,CAACK,OAAO,CAACyB,KAAK;MAAC;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAa;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC/C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEX;AAACY,EAAA,GApCQ5B,OAAO;AAsChB,eAAeA,OAAO;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}